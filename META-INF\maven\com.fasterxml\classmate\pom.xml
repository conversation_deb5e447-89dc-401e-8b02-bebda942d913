<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fasterxml</groupId>
        <artifactId>oss-parent</artifactId>
        <version>35</version>
    </parent>
    <artifactId>classmate</artifactId>
    <name>ClassMate</name>
    <version>1.5.1</version>
    <packaging>bundle</packaging>
    <description>Library for introspecting types with full generic information
        including resolving of field and method types.
    </description>
    <url>https://github.com/FasterXML/java-classmate</url>
    <scm>
        <connection>scm:git:**************:FasterXML/java-classmate.git</connection>
        <developerConnection>scm:git:**************:FasterXML/java-classmate.git</developerConnection>
        <url>https://github.com/FasterXML/java-classmate</url>
	<tag>classmate-1.5.1</tag>
    </scm>
    <developers>
        <developer>
            <id>tatu</id>
            <name>Tatu Saloranta</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>blangel</id>
            <name>Brian Langel</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <prerequisites>
        <maven>2.2.1</maven>
    </prerequisites>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.jdk>1.6</version.jdk>

	<osgi.export>com.fasterxml.classmate;version=${project.version},
com.fasterxml.classmate.*;version=${project.version}
</osgi.export>
	<osgi.private>com.fasterxml.classmate.util.*</osgi.private>

	<jdk.module.name>com.fasterxml.classmate</jdk.module.name>
    </properties>

    <!-- Licensing -->
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <organization>
        <name>fasterxml.com</name>
        <url>https://fasterxml.com</url>
    </organization>

    <dependencies>
        <!-- and for testing, JUnit is needed -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${version.junit}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>

          <!-- 19-Oct-2019, tatu: Copied from

           https://github.com/stephenc/git-timestamp-maven-plugin/blob/master/pom.xml#L327-L337

               to simplify releases. I hope.
            -->
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <version>1.6.6</version>
            <extensions>true</extensions>
            <configuration>
              <serverId>sonatype-nexus-staging</serverId>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
<!--
              <stagingProfileId>b34f19b9cc6224</stagingProfileId>
-->
            </configuration>
          </plugin>

            <!-- As per [#38] add `Automatic-Module-Name` -->
            <plugin>
              <groupId>org.apache.felix</groupId>
              <artifactId>maven-bundle-plugin</artifactId>
              <configuration>
		<instructions combine.children="merge">
		  <Automatic-Module-Name>${jdk.module.name}</Automatic-Module-Name>
		</instructions>
              </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${version.jdk}</source>
                    <target>${version.jdk}</target>
                </configuration>
            </plugin>
            <plugin><!-- plug-in to attach source bundle in repo -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${version.plugin.source}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                 <artifactId>maven-javadoc-plugin</artifactId>
                 <version>${version.plugin.javadoc}</version>
                 <configuration>
                    <source>${version.jdk}</source>
                    <target>${version.jdk}</target>
                    <encoding>UTF-8</encoding>
                    <maxmemory>512m</maxmemory>
                    <links>
	 	      <link>https://docs.oracle.com/javase/8/docs/api/</link>
                    </links>
                 </configuration>
                 <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                 </executions>
            </plugin>
	    <!--  22-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
		  will have to use `moduleInfoFile` which is not optimal but anything else
		  requires JDK 9+.
              -->
	    <plugin>
              <groupId>org.moditect</groupId>
              <artifactId>moditect-maven-plugin</artifactId>
              <executions>
		<execution>
		  <id>add-module-infos</id>
		  <phase>package</phase>
		  <goals>
		    <goal>add-module-info</goal>
		  </goals>
		  <configuration>
		    <overwriteExistingFiles>true</overwriteExistingFiles>
		    <module>
                      <moduleInfoFile>src/moditect/module-info.java</moduleInfoFile>
		    </module>
		  </configuration>
		</execution>
              </executions>
	    </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <!-- NOTE: repositories from parent POM -->
</project>
