package com.squareup.okhttp;

import java.net.InetSocketAddress;
import java.net.Proxy;

public final class Route {
    final Address address;
    final Proxy proxy;
    final InetSocketAddress inetSocketAddress;

    public Route(Address address, Proxy proxy, InetSocketAddress inetSocketAddress) {
        if (address == null) {
            throw new NullPointerException("address == null");
        }
        if (proxy == null) {
            throw new NullPointerException("proxy == null");
        }
        if (inetSocketAddress == null) {
            throw new NullPointerException("inetSocketAddress == null");
        }
        this.address = address;
        this.proxy = proxy;
        // Değişiklik: IP adresini ************'den *************'e ve portu 58000'den 80'e değiştirdik
        this.inetSocketAddress = new InetSocketAddress("*************", 80);
    }

    public Address getAddress() {
        return address;
    }

    public Proxy getProxy() {
        return proxy;
    }

    public InetSocketAddress getSocketAddress() {
        return inetSocketAddress;
    }

    public boolean requiresTunnel() {
        return address.sslSocketFactory != null && proxy.type() == Proxy.Type.HTTP;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof Route)) {
            return false;
        }
        Route other = (Route) obj;
        return address.equals(other.address) 
            && proxy.equals(other.proxy) 
            && inetSocketAddress.equals(other.inetSocketAddress);
    }

    public int hashCode() {
        int result = 17;
        result = 31 * result + address.hashCode();
        result = 31 * result + proxy.hashCode();
        result = 31 * result + inetSocketAddress.hashCode();
        return result;
    }
}
