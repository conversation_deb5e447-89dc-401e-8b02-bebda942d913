{"swagger": "2.0", "info": {"description": "ZenZefi REST API provides operations for resources certificate, configuration, log, user, system.\nThe operation-handler endpoints link to the enabled Spring Boot Actuator endpoints. For more details please check:\nhttps://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html", "version": "Latest", "title": "ZenZefi REST API"}, "host": "localhost:61000", "basePath": "/", "tags": [{"name": "ZenZefi Certificate Management", "description": "Zenzefi Certificates Resource"}, {"name": "ZenZefi Configuration Management", "description": "Configurations Resource"}, {"name": "ZenZefi Logging Management", "description": "Logs Resource"}, {"name": "ZenZefi System Management", "description": "Zen Zefi System Resource"}, {"name": "ZenZefi User Management", "description": "Users Resource"}, {"name": "operation-handler", "description": "Operation Handler"}], "paths": {"/certificates": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Returns an array of certificates belonging to the current logged in user.", "operationId": "all", "produces": ["*/*", "application/json"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCertificate"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}, "delete": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Deletes all certificates from current user-specific certificate store, or by authority key identifier (or part number) and serial number. For details about the parameters, please view the request model.", "operationId": "deleteCertificates", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "all", "in": "query", "description": "If set to true, deletes all certificates.", "required": false, "type": "boolean", "default": false, "allowEmptyValue": false, "x-example": false}, {"in": "body", "name": "pairs", "description": "Base64 encoded authority key identifier (or part number) / serial number pairs.", "required": false, "schema": {"type": "array", "items": {"$ref": "#/definitions/DeleteCertificateModel"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedDeleteCertificatesResult"}}}, "400": {"description": "Empty input list. | Invalid authority key identifier. The length is 20 bytes. | The input is not Base64 encoded. | Invalid serial number. The max length is 16 bytes. | The certificate with id does not exist in current user-specific certificate store."}, "404": {"description": "Delete certificates called but no match was found."}}, "deprecated": false}}, "/certificates/activeForTesting": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Getting the active for testing certificates", "operationId": "activeForTestingCertificates", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ActiveForTestingCertificates"}}}, "deprecated": false}}, "/certificates/activeForTesting/activate/{id}": {"put": {"tags": ["ZenZefi Certificate Management"], "summary": "Activating certificate to be used in testing mechanism. The path variable 'id' is ZenZefi internal id of the certificate.", "operationId": "activateForTesting", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "Internal ID in ZenZefi", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Not found new certificate active for testing. | Not found old certificate active for testing. | Cannot switch active for testing."}, "406": {"description": "Operation not allowed while certificates update process is active."}, "500": {"description": "Invalid configuration."}}, "deprecated": false}}, "/certificates/activeForTesting/activate/{id}/{useCase}": {"put": {"tags": ["ZenZefi Certificate Management"], "summary": "Activating certificate to be used in testing mechanism. The use case can be: G, VSM, RP. G=General use case, VSM= VSM use cases, RP = Replacement Package use case. The path variable 'id' is ZenZefi internal id of the certificate.", "operationId": "activateForTestingBasedOnUseCase", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "Internal ID in ZenZefi", "required": false, "type": "string"}, {"name": "useCase", "in": "path", "description": "G=General use case, VSM= VSM use cases, RP = Replacement Package use case", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Not found new certificate active for testing. | Not found old certificate active for testing. | Cannot switch active for testing."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/activeForTesting/deactivate/{id}": {"put": {"tags": ["ZenZefi Certificate Management"], "summary": "Deactivating certificate usage in testing mechanism. The path variable 'id' is Zenzefi internal id of the certificate.", "operationId": "deactivateUpdateForTesting", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "Internal ID in ZenZefi", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Not found new certificate active for testing. | Not found old certificate active for testing. | Cannot switch active for testing."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/activeForTesting/deactivate/{id}/{useCase}": {"put": {"tags": ["ZenZefi Certificate Management"], "summary": "Deactivating certificate usage in testing mechanism. The use case can be: G, VSM, RP. G=General use case, VSM= VSM use cases, RP = Replacement Package use case. The path variable 'id' is ZenZefi internal id of the certificate.", "operationId": "deactivateUpdateForTestingBasedOnUseCase", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "Internal ID in ZenZefi", "required": false, "type": "string"}, {"name": "useCase", "in": "path", "description": "G=General use case, VSM= VSM use cases, RP = Replacement Package use case", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Not found new certificate active for testing. | Not found old certificate active for testing. | Cannot switch active for testing."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/activeForTesting/enabled": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Checking if active for testing is enabled", "operationId": "activeForTestingEnabled", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}, "deprecated": false}}, "/certificates/activeForTesting/enhanced": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Getting the active for testing enhanced certificates", "operationId": "activeForTestingCertificatesEnhanced", "produces": ["*/*"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/Certificate"}}}}, "deprecated": false}}, "/certificates/activeForTesting/options/{type}": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Getting the certificates base on type", "operationId": "certificatesByType", "produces": ["*/*"], "parameters": [{"name": "queryMap", "in": "query", "description": "queryMap", "required": true, "items": {"type": "object", "additionalProperties": {"type": "string"}}}, {"name": "type", "in": "path", "description": "type", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCertificate"}}}}, "deprecated": false}}, "/certificates/activeForTesting/usecases/{type}": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Getting the active for testing certificates", "operationId": "activeForTestingCertificatesBasedOnUseCase", "produces": ["*/*"], "parameters": [{"name": "type", "in": "path", "description": "type", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificatesUseCaseHolder"}}}, "deprecated": false}}, "/certificates/activeForTesting/{id}": {"put": {"tags": ["ZenZefi Certificate Management"], "summary": "Switches the state of active for testing  based on certificate's id and type. The type should be known by ZZ server.", "operationId": "updateActiveForTesting", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "activeForTesting", "in": "path", "description": "New selected certificate ID, old certificate ID(one which is deselected) and a boolean set to true if there is no previously checked certificate.", "required": true, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Not found new certificate active for testing. | Not found old certificate active for testing. | Cannot switch active for testing."}, "406": {"description": "Operation not allowed while certificates update process is active."}, "500": {"description": "Invalid configuration."}}, "deprecated": false}}, "/certificates/checkSystemIntegrity": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Makes system integrity check and downloads the report. Endpoint used only in the ZenZefi CLI.", "operationId": "checkSystemIntegrity", "produces": ["*/*", "application/xml"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/certificates/checkSystemIntegrityLog": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Returns the system integrity check XML report. Endpoint used only in the ZenZefi UI.", "operationId": "getIntegrityCheckLogFile", "produces": ["application/xml"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/File"}}, "400": {"description": "System integrity check xml report not found"}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/checkSystemIntegrityLogExistance": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Checks whether the system integrity report is still existent. Endpoint used only in the ZenZefi UI.", "operationId": "isIntegrityCheckLogXMLExistent", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/certificates/checkSystemIntegrityReport": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Check system integrity without downloading the XML report. Endpoint used only in the ZenZefi UI.", "operationId": "checkSystemIntegrityWithoutReport", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SystemIntegrityCheckResultWithoutReport"}}}, "deprecated": false}}, "/certificates/details/{id}": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Returns a json representation of required certificate by id. The id must be a valid UUID.", "operationId": "getCertificateForDetailsPanelUsingGET", "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "The id of the certificate. Must be valid UUID.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ZenZefiCertificate"}}, "400": {"description": "Certificate does not exist | Invalid format for the given ID."}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/diagCertForCentralAuthentication": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Diagnostic Authentication certificates for central authentication are regular Diagnostic Authentication certificates, which however must not be restricted to a specific ECU and which must not contain certain user roles (individually configurable)", "operationId": "diagCertForCentralAuthentication", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes. Part number is also supported (10 characters, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedCertificateWithSNAndUserRoleResult"}}}, "deprecated": false}}, "/certificates/export/{id}": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "endpoint export certificate", "operationId": "exportCertificate", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "The id of the certificate. Must be valid UUID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Export Certificate successfully executed.", "schema": {"$ref": "#/definitions/CertificateHexFormatOutput"}}}, "deprecated": false}}, "/certificates/extendedValidation": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Enable/Disable extended validation", "operationId": "enableExtendedValidationV3", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "enablement", "description": "enablement", "required": true, "schema": {"$ref": "#/definitions/Enablement"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/certificates/filter": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Returns an array of filtered certificates belonging to the current logged in user. This endpoint is used in the ZenZefi UI, for filtering the certificates table.", "operationId": "filter", "produces": ["*/*", "application/json"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "allRequestParams", "in": "query", "description": "The filtering parameters, which also includes the limit for the query.", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}, "allowEmptyValue": false}, {"name": "validFrom", "in": "query", "description": "Valid from date. The date format is yyyy-MM-dd.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi", "allowEmptyValue": false}, {"name": "validTo", "in": "query", "description": "Valid to date. The date format is yyyy-MM-dd.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/Certificate"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/filter/options": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Filter options", "operationId": "filterOptions", "produces": ["*/*", "application/json"], "parameters": [{"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "columnName", "in": "query", "description": "column for which the options are needed", "required": false, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/import/files": {"post": {"tags": ["ZenZefi Certificate Management"], "summary": "Not working from Swagger UI and Swagger Generated API. Imports certificates, .crt or .p12 files into current logged in user-specific certificate store. Operation used in the ZenZefi UI for importing.", "operationId": "Import Files", "consumes": ["multipart/form-data"], "produces": ["application/json"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "files", "in": "formData", "description": "files", "required": true, "type": "array", "items": {"type": "file"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ImportResult"}}}, "400": {"description": "Cannot read the multipart data from input"}}, "deprecated": false}}, "/certificates/import/fromLocal": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Import certificates into current user-specific certificate store. For this endpoint the inputs are the certificate or PKCS12 file path, and password in the case of PKCS12.", "operationId": "importCertificatesFromPathsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "localImportInputs", "description": "File path / password pairs. The file path has to provided with single slashes as separator, e.g. c:/folder/folder.ext - mind: single backslashes can not be used.", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/LocalImportInput"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ImportResult"}}}, "400": {"description": "Cannot read the file from path"}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/importProductionCerts": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Import encrypted PKCS#12 packages. The packages are exported from SigModul, and encrypted with the public key generated by ZenZefi. The packages are Base64 encoded. For details about the parameters, please view the request model.", "operationId": "importEncryptedPKCSPackage", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "pkcsInput", "description": "Base64 encoded backend subject key identifier, encrypted PKCS package generated by SigModul.", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/EncryptedPKCSPackageInput"}}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/EncryptedPKCSImportResult"}}, "400": {"description": "User key pair for production not found. | Failed to decode the backend subject key identifier. | Failed to decode PKCS package for backend with subject key identifier. | Decryption of Certificate Package failed for backend with subject key identifier."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/list": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Returns an array of certificates belonging to the current logged in user.", "operationId": "listCertificates", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCertificateSummary"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/partnumbermapping": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Returns for a given part number the corresponding BSKI, and vice versa", "operationId": "partNumberMapping", "produces": ["application/json;charset=UTF-8", "application/asc"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "identifier", "in": "query", "description": "The part number or the BSKI. Type is determined from the value, therefore must either be a valid BSKI or a valid part number.", "required": false, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/PartNumberMappingResult"}}}, "400": {"description": "Invalid format for the given identifier | The backend found with the identifier: XYZ does not have a part number"}, "404": {"description": " No mapping entry found for part number / bski"}, "500": {"description": "Internal error message"}}, "deprecated": false}}, "/certificates/restore": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Restore root and backend certificates from application binaries.", "operationId": "restoreCertificates", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "406": {"description": "Operation not allowed while certificates update process is active."}, "500": {"description": "Restoring certificates failed"}}, "deprecated": false}}, "/certificates/rootsAndBackends": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Returns the root and backend certificates for the currently logged in user.", "operationId": "getRootAndBackends", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "rootOrBackend", "in": "query", "description": "Which certificates should be retrieved: BACKEND, ROOT. Parameter is optional, if none is provided all are returned.", "required": false, "type": "string", "allowEmptyValue": false, "enum": ["ROOT", "BACKEND"]}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/RootOrBackendResult"}}}}, "deprecated": false}}, "/certificates/search": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Retrieve certificate by authority key identifier (or part number) and serial number.", "operationId": "getCertByID", "produces": ["*/*"], "parameters": [{"name": "AuthorityKeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes. Part number is also supported (10 characters, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "SerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedCertificateResult"}}, "404": {"description": "No certificate was found with given authority key identifier and serial number."}}, "deprecated": false}}, "/certificates/search/certificateReplacementPackage": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Get certificate replacement package based on given criteria. For details about the parameters, please view the request model.", "operationId": "certificateReplacementPackage", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "input", "description": "Base64 encoded certificate bytes, target backend subject key identifier (or part number), unique ECU ids and targetVIN (used to download ECU from PKI if doesn't exist in user store)", "required": true, "schema": {"$ref": "#/definitions/CertificateReplacementPackageV3Input"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateReplacementPackageExtendedResult"}}, "404": {"description": "Certificate replacement package not found. | No Certificate was found matching the filter criteria."}}, "deprecated": false}}, "/certificates/search/diag": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Get Diagnostic Certificate based on given criteria.", "operationId": "diagCert", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes. Part number is also supported (10 characters, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "UserRole", "in": "query", "description": "The role of the user: 1=Supplier, 2=Development ENHANCED, 3=Production, 4=After-Sales ENHANCED, 5=After-Sales STANDARD, 6=After-Sales BASIC, 7=Internal Diagnostic Test Tool, 8=ePTI Test Tool", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedCertificateWithSNAndUserRoleResult"}}, "400": {"description": "Invalid subject key identifier. The length is 20 bytes. | Invalid target VIN. The length is 17 bytes. | Invalid target ECU. The max length is 30 bytes."}, "404": {"description": "No Certificate was found matching the filter criteria."}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/search/diag/active": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Checks whether a call to get diagnosis certificate will return the certificate with the serial number provided as parameter.", "operationId": "checkActiveDiagCert", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes. Part number is also supported (10 characters, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "DiagCertSerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "UserRole", "in": "query", "description": "The role of the user: 1=Supplier, 2=Development ENHANCED, 3=Production, 4=After-Sales ENHANCED, 5=After-Sales STANDARD, 6=After-Sales BASIC, 7=Internal Diagnostic Test Tool, 8=ePTI Test Tool", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "400": {"description": "Invalid subject key identifier. The length is 20 bytes. | Invalid serial number. The max length is 16 bytes. | Invalid target VIN. The length is 17 bytes. | Invalid target ECU. The max length is 30 bytes."}, "404": {"description": "No Certificate was found matching the filter criteria."}}, "deprecated": false}}, "/certificates/search/diag/checkOwnership": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Searches for a certificate with the backend subject key identifier (or part number) and serial number from the request and calculates the signature for ECUChallenge. For details about the parameters, please view the request model.", "operationId": "checkOwnership", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "checkOwnershipInput", "description": "Base64 encoded backend subject key identifier (or part number), Base64 encoded ecu challenge, Base64 encoded serial number.", "required": true, "schema": {"$ref": "#/definitions/CheckOwnershipInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Ownership"}}, "400": {"description": "CheckOwnership validation failed."}, "404": {"description": "No certificate with given serial number found."}, "409": {"description": "More than one certificate found"}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/search/diag/role": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Retrieve diagnostic certificate role by authority key identifier (or part number) and serial number.", "operationId": "getDiagCertIdRole", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes. Part number is also supported (10 characters, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "SerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateUserRoleResult"}}, "404": {"description": "No certificate was found with given authority key identifier and serial number."}}, "deprecated": false}}, "/certificates/search/enhRights": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Retrieve an enhanced rights certificate based on given criteria.", "operationId": "enhRights", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes. Part number is also supported (as 10 character string, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "DiagCertSerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedCertificateWithSNAndIssuerSNResult"}}}, "404": {"description": "No Certificate was found matching the filter criteria.", "schema": {"$ref": "#/definitions/CEBASResult"}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/search/enhRightsIds": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Determine Enhanced Rights Certificates for dedicated services. Search for Enhanced Rights certificates in the user store containing specific services that can be used for Enhanced Rights Certificates to release these services in the ECU. If ZenZefi finds one or more results, i.e. Enhanced Rights certificates which match, the serial numbers of the holder certificates and the Enhanced Rights certificates will be returned.", "operationId": "getEnhRightId", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes. Part number is also supported (as 10 character string, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "services", "in": "query", "description": "The services - comma separated. Values in HEX (eg ff, ff01, ff010203).", "required": true, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/EnhancedRightsResponse"}}, "404": {"description": " An Enhanced Rights Certificate containing the requested services has not been found."}}, "deprecated": false}}, "/certificates/search/secOCISCert": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Retrieve a Sec OCIS certificate based on given criteria. For details about the parameters, please view the request model.", "operationId": "secOCISCert", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "secOCISInput", "description": "Base64 encoded backend subject key identifier (or part number), Base64 encoded diag serial number, Base64 encoded ECU certificate, target ECU, target VIN.", "required": true, "schema": {"$ref": "#/definitions/SecOCISInputV3"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedCertificateWithSNResult"}}, "404": {"description": "Could not find diagnostic certificate."}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/search/secureVariantCoding": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Retrieve a variant coding user certificate based on given criteria. For details about the parameters, please view the request model.", "operationId": "secureVariantCoding", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "secureVariantCodingInput", "description": "Base64 encoded backend subject key identifier (or part number), Base64 representation of the data to be signed, target ECU, target VIN.", "required": true, "schema": {"$ref": "#/definitions/SecureVariantCodingInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedVariantCoding"}}, "400": {"description": "Signing Coding String failed"}, "404": {"description": "No Certificate was found matching the filter criteria."}}, "deprecated": false}}, "/certificates/search/timeCert": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Retrieve a time certificate based on given criteria.", "operationId": "time<PERSON>ert", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes. Part number is also supported (as 10 character string, NOT Base64 encoded).", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "<PERSON><PERSON>", "in": "query", "description": "The nonce. Must be Base64 encoded and have the length of 32 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedCertificateWithSNResult"}}, "400": {"description": "Invalid format for the given ID. | Invalid subject key identifier. The length is 20 bytes. | Invalid authority key identifier. The length is 20 bytes. | Invalid nonce. The length is 32 bytes. | The input is not Base64 encoded. | Invalid target ECU. The max length is 30 bytes. | Invalid target VIN. The length is 17 bytes."}, "404": {"description": "No certificate found. | No certificate found for backend subject key identifier when creating CSR."}, "406": {"description": "Parent Certificate type is invalid! Only Backend Root and Diagnostic Authentication Certificate supported"}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/certificates/signECU": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Signs a challenge byte array with the private key of an ECU certificate to prove the ownership of the certificate.", "operationId": "signEcuRequest", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "input", "description": "ECU sign request input.", "required": true, "schema": {"$ref": "#/definitions/EcuSignRequestInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExtendedEcuSignRequestResult"}}, "400": {"description": "The input is not Base64 encoded. | Backend subject key identifier is mandatory. | Challenge byte array is mandatory. | Invalid Unique ECU ID. | Certificate with authority key identifier and unique ECU ID not found. | No key pair found for certificate with ID. Certificate with authority key identifier and serialNo not found."}}, "deprecated": false}}, "/certificates/signatureCheck": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Checks message signature, signed by the input certificate. For details about the parameters, please view the request model.", "operationId": "signatureCheck", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "signatureCheckModel", "description": "Base64 encoded ECU certificate, Base64 encoded message, Base64 encoded signature.", "required": true, "schema": {"$ref": "#/definitions/SignatureCheckHolder"}}], "responses": {"200": {"description": "Signature check succeeded.", "schema": {"type": "boolean"}}, "400": {"description": "Could not verify signature for message. | Wrong input. All parameters are mandatory."}, "404": {"description": "Could not create certificate from bytes input stream. | Could not check signature of the message for an enhanced rights certificate, no public key."}, "500": {"description": "Signature check failed."}}, "deprecated": false}}, "/certificates/signatures": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Sign coding data for all backends with available Variant Coding User certificates.", "operationId": "signatures", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "createSignatureInput", "description": "Base64 encoded coding data, ecu, vin", "required": true, "schema": {"$ref": "#/definitions/ZenZefiCreateSignatureInput"}}], "responses": {"200": {"description": "Sign request successfully processed.", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCreateSignatureResult"}}}, "500": {"description": "Sign request couldn't be processed."}}, "deprecated": false}}, "/certificates/update/active": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Returns whether or not the update operation is active.", "operationId": "isUpdateActive", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}, "deprecated": false}}, "/certificates/update/cancel": {"post": {"tags": ["ZenZefi Certificate Management"], "summary": "Update process cancellation", "operationId": "cancelUpdate", "consumes": ["application/json"], "produces": ["*/*"], "responses": {"200": {"description": "OK"}, "406": {"description": "Update session cannot be cancelled since is not running", "schema": {"$ref": "#/definitions/CEBASResult"}}}, "deprecated": false}}, "/certificates/update/differential": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Perform differential update of the certificates. Only those certificates in the user-specific certificate store.", "operationId": "updateDifferentialCertificates", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Registered user needs to be logged in."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/update/full": {"post": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Perform full update of the certificates. All certificates the user has rights for are requested independently of the certificate renewal time.", "operationId": "updateFullCertificates", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Registered user needs to be logged in."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/update/metrics": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Returns the metrics during the certificates update process", "operationId": "updateCertificatesMetrics", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UpdateCertificateMetrics"}}}, "deprecated": false}}, "/certificates/{ids}": {"delete": {"tags": ["ZenZefi Public API", "ZenZefi Certificate Management"], "summary": "Deletes one or more certificates from current user-specific certificate store. The deletion is made by certificate ids, and each certificate id must be a valid UUID. The ids are comma separated.", "operationId": "deleteCertificatesUsingIds", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "ids", "in": "path", "description": "The ids of the certificates, comma separated. Must be valid UUIDs.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedDeleteCertificatesResult"}}}, "400": {"description": "Invalid format for the given ID. | Delete certificates called but no match was found. | The certificate with id does not exist in current user-specific certificate store."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/certificates/{id}": {"get": {"tags": ["ZenZefi Certificate Management"], "summary": "Returns a json representation of required certificate by id. The id must be a valid UUID.", "operationId": "getCertificateUsingGET", "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "The id of the certificate. Must be valid UUID.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Certificate"}}, "400": {"description": "Certificate does not exist | Invalid format for the given ID."}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/configurations": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Returns an array of configurations, which are attached to current user (Default or Registered). Endpoint used in the ZenZefi UI.", "operationId": "getUserConfigurationsUsingGET", "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "object"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/configurations/activeProfiles": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Returns current active profiles", "operationId": "activeProfiles", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}, "deprecated": false}}, "/configurations/applicationVersion": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Returns the application version.", "operationId": "getApplicationVersion", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/configurations/autologout": {"put": {"tags": ["ZenZefi Configuration Management"], "summary": "Activates or deactivates automatically logout.", "operationId": "updateAutologout", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "activate", "description": "activate", "required": true, "schema": {"$ref": "#/definitions/Enablement"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/configurations/certificatesColumnOrder": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Gets the column order of the certificates table for the current logged user. Endpoint used in the ZenZefi UI.", "operationId": "getCertificatesColumnOrder", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Configuration Management"], "summary": "Sets the column order of the certificates table for the current logged user. Endpoint used in the ZenZefi UI.", "operationId": "setGertificatesColumnOrder", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "columnOrder", "description": "The column order.", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}}, "/configurations/certificatesColumnVisibility": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Gets the column visibility of the certificates table for the current logged user. Endpoint used in the ZenZefi UI.", "operationId": "getCertificatesColumnVisibility", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Configuration Management"], "summary": "Sets the column visibility of the certificates table for the current logged user. Endpoint used in the ZenZefi UI.", "operationId": "setCertificatesColumnVisibility", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "columnVisibilities", "description": "Column visibilities.", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}}, "/configurations/detailsPanelState": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Gets the details pane state of the certificates table for the current logged user. Endpoint used in the ZenZefi UI.", "operationId": "getDetailsPanelState", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}}, "/configurations/detailsPanelState/{state}": {"post": {"tags": ["ZenZefi Configuration Management"], "summary": "Sets the details pane state of the certificates table for the current logged user. Endpoint used in the ZenZefi UI.", "operationId": "setDetailsPanelState", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "state", "in": "path", "description": "The details pane state. Open or closed.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}}, "/configurations/general": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Returns an array of general configurations, which are attached to default user. Endpoint used in the ZenZefi UI.", "operationId": "getGeneralConfigurationsUsingGET", "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "object"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/configurations/maxRowsPerPage": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Gets the max rows for pagination. Endpoint used in the ZenZefi UI, in the logs table.", "operationId": "getPaginationMaxRows", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Configuration Management"], "summary": "Updates the max rows for pagination. Endpoint used in the ZenZefi UI, in the logs table.", "operationId": "updatePaginationMaxRows", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "maxRowsPerPage", "description": "The maximum number of rows for pagination. Endpoint used in the ZenZefi UI, in the logs table. Valid values: integer values.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "deprecated": false}}, "/configurations/pkiLogoutUrl": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Returns the PKI Logout URL.", "operationId": "getPKILogoutURL", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/configurations/proxy": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Gets the server proxy.", "operationId": "getProxy", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Only the default user can set the proxy", "schema": {"type": "string"}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Configuration Management"], "summary": "Sets the server proxy.", "operationId": "setProxy", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "proxy", "description": "Proxy value.", "required": true, "schema": {"$ref": "#/definitions/ProxyInput"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Only the default user can set the proxy", "schema": {"type": "string"}}}, "deprecated": false}}, "/configurations/proxyType": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Gets the server proxy type.", "operationId": "getProxyTypeUsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Only the default user can get the proxy type", "schema": {"type": "string"}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Configuration Management"], "summary": "Sets the server proxy type.", "operationId": "setProxyTypeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "proxyType", "description": "Proxy type value.", "required": true, "schema": {"$ref": "#/definitions/ProxyTypeInput"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Only the default user can set the proxy type", "schema": {"type": "string"}}}, "deprecated": false}}, "/configurations/proxyWithType": {"post": {"tags": ["ZenZefi Configuration Management"], "summary": "Sets the server proxy and the proxy type.", "operationId": "setProxyWithTypeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "proxy", "description": "Proxy value.", "required": true, "schema": {"$ref": "#/definitions/ProxyWithType"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Only the default user can set the proxy", "schema": {"type": "string"}}}, "deprecated": false}}, "/configurations/rolesPriority": {"get": {"tags": ["ZenZefi Configuration Management"], "summary": "Returns an array of user roles, attached to current user (default or registered). Endpoint used in the ZenZefi UI.", "operationId": "getRolePriorityConfigurationOfCurrentUserUsingGET", "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "object"}}}, "406": {"description": "Operation not allowed while certificates update process is active."}, "500": {"description": "Internal server error"}}, "deprecated": false}, "post": {"tags": ["ZenZefi Configuration Management"], "summary": "Updates user roles configuration, for current user (default or registered). Endpoint used in the ZenZefi UI.", "operationId": "updateConfigurationRolesPriorityOfCurrentUserUsingPOST", "consumes": ["application/json"], "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "configuration", "description": "The configuration.", "required": true, "schema": {"$ref": "#/definitions/Configuration"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Configuration"}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/configurations/{id}": {"put": {"tags": ["ZenZefi Configuration Management"], "summary": "Update configuration. Endpoint used in the ZenZefi UI.", "operationId": "updateConfigurationUsingPUT", "consumes": ["application/json"], "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "configuration", "description": "The configuration.", "required": true, "schema": {"$ref": "#/definitions/Configuration"}}, {"name": "id", "in": "path", "description": "The id of the configuration. Must be a valid UUID.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Configuration"}}, "406": {"description": "Operation not allowed while certificates update process is active."}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/distinctLogLevels": {"get": {"tags": ["ZenZefi Logging Management"], "summary": "Returns the distinct log levels which are in the database. Endpoint used for the filtering on the UI logs table.", "operationId": "getDistinctLogLevelsUsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}, "deprecated": false}}, "/health": {"get": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingGET_1", "produces": ["application/vnd.spring-boot.actuator.v3+json", "application/json", "application/vnd.spring-boot.actuator.v2+json"], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}, "deprecated": false}}, "/health/**": {"get": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingGET", "produces": ["application/vnd.spring-boot.actuator.v3+json", "application/json", "application/vnd.spring-boot.actuator.v2+json"], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}, "deprecated": false}}, "/logs": {"get": {"tags": ["ZenZefi Logging Management"], "summary": "Downloads all the logs. Endpoint used in the ZenZefi UI, logs table.", "operationId": "allUsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CEBASLog"}}}}, "deprecated": false}}, "/logs/archive": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Logging Management"], "summary": "Archives and downloads logs.", "operationId": "getLogArchive", "produces": ["application/json;charset=UTF-8", "application/zip"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/File"}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Logging Management"], "summary": "Archives and downloads logs.", "operationId": "archiveUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "path", "description": "The absolute path of where the archive should be saved.", "required": true, "schema": {"$ref": "#/definitions/ArchivePath"}}], "responses": {"200": {"description": "OK"}}, "deprecated": false}}, "/logs/filter": {"get": {"tags": ["ZenZefi Logging Management"], "summary": "Filters the ZenZefi logs table. Endpoint used only in the ZenZefi UI.", "operationId": "getFilteredLogsUsingGET", "produces": ["*/*", "application/json"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "Range", "in": "header", "description": "Log filter range. E.g. items=0-9", "required": true, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "allRequestParams", "in": "query", "description": "The filtering parameters, which also includes the limit for the query.", "required": false, "items": {"type": "object", "additionalProperties": {"type": "string"}}, "allowEmptyValue": false}, {"name": "createTimestamp", "in": "query", "description": "Log create timestamp, sent as from date and to date pair.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CEBASLog"}}}, "400": {"description": "Missing range"}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/logs/getLogFile": {"get": {"tags": ["ZenZefi Public API", "ZenZefi Logging Management"], "summary": "Get file with logs created since the last call. First call will return all available log entries.", "operationId": "getLogFile", "produces": ["application/xml"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/File"}}, "500": {"description": "Download log file failed."}}, "deprecated": false}}, "/logs/logLevel": {"get": {"tags": ["ZenZefi Logging Management"], "summary": "Get configured log level for all the specified application loggers.", "operationId": "getConfiguredLogLevel", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}, "post": {"tags": ["ZenZefi Logging Management"], "summary": "Set configured log level for all the specified application loggers.", "operationId": "setConfiguredLogLevelToLogging", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "LogLevel", "in": "query", "description": "The logging level. Accepted values: FINE, FINER, FINEST, INFO.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK"}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/logs/loggers": {"get": {"tags": ["ZenZefi Logging Management"], "summary": "Get application loggers.", "operationId": "applicationLoggers", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}, "deprecated": false}}, "/logs/logsIntegrity": {"get": {"tags": ["ZenZefi Logging Management"], "summary": "Returns false if a single log entry was deleted from the database, true otherwise.", "operationId": "validateLogsIntegrity", "produces": ["*/*", "JSON"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "cached", "in": "query", "description": "cached", "required": false, "type": "boolean", "default": true}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}}, "deprecated": false}}, "/shutdown": {"post": {"tags": ["operation-handler"], "summary": "handle", "operationId": "handleUsingPOST", "consumes": ["application/json"], "produces": ["application/vnd.spring-boot.actuator.v3+json", "application/json", "application/vnd.spring-boot.actuator.v2+json"], "parameters": [{"in": "body", "name": "body", "description": "body", "required": false, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}, "deprecated": false}}, "/system/generateKeyPair": {"get": {"tags": ["ZenZefi Public API", "ZenZefi System Management"], "summary": "Key Pair generation. Used for communication with other systems. SigModul uses the generated public key to encrypt PKCS packages.", "operationId": "generateKeyPairUsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "The public key base64 encoded.", "schema": {"type": "string"}}, "401": {"description": "Unauthorized operation 'generateKeyPair' for registered user."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": false}}, "/system/generateTesterNonce": {"get": {"tags": ["ZenZefi Public API", "ZenZefi System Management"], "summary": "Secure Random Nonce generated fro testers", "operationId": "generateTesterNonce", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "The nonce base64 encoded.", "schema": {"type": "string"}}}, "deprecated": false}}, "/system/installationDetails": {"get": {"tags": ["ZenZefi System Management"], "summary": "Returns the instalation details of the running ZenZefi server.", "operationId": "installationDetailsUsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/InstallationDetails"}}}}, "deprecated": false}}, "/system/performance": {"get": {"tags": ["ZenZefi System Management"], "summary": "List performance audit entries.", "operationId": "listPerformanceAuditEntries", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/PerformanceAuditSummary"}}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/system/publicKey": {"get": {"tags": ["ZenZefi Public API", "ZenZefi System Management"], "summary": "System's public key. It is used in encrypted communication between systems", "operationId": "getSystemPublickeyUsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "The public key base64 encoded.", "schema": {"type": "string"}}}, "deprecated": false}}, "/system/version": {"get": {"tags": ["ZenZefi Public API", "ZenZefi System Management"], "summary": "Gets ZenZefi server version and api version.", "operationId": "getVersionUsingGET", "produces": ["*/*", "application/json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Version"}}, "500": {"description": "Internal server error"}}, "deprecated": false}}, "/users": {"get": {"tags": ["ZenZefi User Management"], "summary": "Returns a list of users.", "operationId": "allUsers", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/UserData"}}}}, "deprecated": false}, "delete": {"tags": ["ZenZefi User Management"], "summary": "Deletes user account by username.", "operationId": "deleteUserByUsername", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "username", "in": "query", "description": "The user name. Length between 1 and 7 characters. Must be alpha numeric, non blank.", "required": true, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}, "/users/currentUser": {"get": {"tags": ["ZenZefi Public API", "ZenZefi User Management"], "summary": "Checks if the current user is the default user. Endpoint used in the ZenZefi UI for automated logout.", "operationId": "currentUser", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UserDetailsWithSession"}}}, "deprecated": false}}, "/users/currentUser/timer/reset": {"post": {"tags": ["ZenZefi User Management"], "summary": "Resets the session timeout for the current user session and returns the user details.", "operationId": "resetSessionTimeout", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UserDetailsWithSession"}}}, "deprecated": false}}, "/users/login": {"post": {"tags": ["ZenZefi Public API", "ZenZefi User Management"], "summary": "Local login for an exiting user in based on the provided username and password.", "operationId": "login", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "user", "description": "The user.", "required": true, "schema": {"$ref": "#/definitions/UserLoginRequest"}}], "responses": {"200": {"description": "Login success.", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "406": {"description": "Login error."}, "412": {"description": "User already logged in."}, "500": {"description": "Exception message"}}, "deprecated": false}}, "/users/login/errors": {"get": {"tags": ["ZenZefi User Management"], "summary": "Login errors", "operationId": "currentLoginErrorMessages", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}, "deprecated": false}}, "/users/logout": {"post": {"tags": ["ZenZefi Public API", "ZenZefi User Management"], "summary": "Log out a user from ZenZefi.", "operationId": "logout", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": false}}, "/users/register": {"post": {"tags": ["ZenZefi User Management"], "summary": "Registers a new user in ZenZefi.", "operationId": "registration", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "user", "description": "The user.", "required": true, "schema": {"$ref": "#/definitions/ZenZefiUser"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "201": {"description": "Registration successful.", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "406": {"description": "User validation failed for a field."}, "500": {"description": "Exception message"}}, "deprecated": false}}, "/users/{ids}": {"delete": {"tags": ["ZenZefi User Management"], "summary": "Deletes user accounts by id. The ids must valid UUIDs.", "operationId": "deleteUsers", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "ids", "in": "path", "description": "Users ids, must be valid UUIDs, comma separated.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": false}}}, "definitions": {"ASN1Encodable": {"type": "object", "title": "ASN1Encodable"}, "ASN1ObjectIdentifier": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "id": {"type": "string"}}, "title": "ASN1ObjectIdentifier"}, "ASN1Set": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "objects": {"$ref": "#/definitions/Enumeration"}}, "title": "ASN1Set"}, "ActiveForTestingCertificates": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "title": "ActiveForTestingCertificates"}, "AlgorithmIdentifier": {"type": "object", "properties": {"algorithm": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "encoded": {"type": "string", "format": "byte"}, "parameters": {"$ref": "#/definitions/ASN1Encodable"}}, "title": "AlgorithmIdentifier"}, "ArchivePath": {"type": "object", "required": ["path"], "properties": {"path": {"type": "string", "description": "The absolute path of where the archive should be saved."}}, "title": "ArchivePath"}, "Attribute": {"type": "object", "properties": {"attrType": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "attrValues": {"$ref": "#/definitions/ASN1Set"}, "attributeValues": {"type": "array", "items": {"$ref": "#/definitions/ASN1Encodable"}}, "encoded": {"type": "string", "format": "byte"}}, "title": "Attribute"}, "AttributeCertificateHolder": {"type": "object", "properties": {"digestAlgorithm": {"$ref": "#/definitions/AlgorithmIdentifier"}, "digestedObjectType": {"type": "integer", "format": "int32"}, "entityNames": {"type": "array", "items": {"$ref": "#/definitions/X500Name"}}, "issuer": {"type": "array", "items": {"$ref": "#/definitions/X500Name"}}, "objectDigest": {"type": "string", "format": "byte"}, "otherObjectTypeID": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "serialNumber": {"type": "integer"}}, "title": "AttributeCertificateHolder"}, "AttributeCertificateIssuer": {"type": "object", "properties": {"names": {"type": "array", "items": {"$ref": "#/definitions/X500Name"}}}, "title": "AttributeCertificate<PERSON>ssuer"}, "AttributeTypeAndValue": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "type": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "value": {"$ref": "#/definitions/ASN1Encodable"}}, "title": "AttributeTypeAndValue"}, "CEBASLog": {"type": "object", "properties": {"checksum": {"type": "string"}, "createTimestamp": {"type": "integer", "format": "int64", "xml": {"name": "createTimestamp", "attribute": false, "wrapped": false}}, "createUser": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "type": {"type": "string"}, "valid": {"type": "boolean"}}, "title": "CEBASLog"}, "CEBASResult": {"type": "object", "properties": {"errorMessage": {"type": "string"}}, "title": "CEBASResult"}, "Certificate": {"type": "object", "properties": {"algorithmIdentifier": {"type": "string"}, "authorityKeyIdentifier": {"type": "string"}, "baseCertificateID": {"type": "string"}, "basicConstraints": {"type": "string"}, "basicConstraintsText": {"type": "string"}, "certificateData": {"$ref": "#/definitions/RawData"}, "hasChildren": {"type": "boolean"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "issuerSerialNumber": {"type": "string"}, "keyUsage": {"type": "array", "items": {"type": "boolean"}}, "keyUsageText": {"type": "string"}, "nonce": {"type": "string"}, "parentId": {"type": "string"}, "partNumber": {"type": "string"}, "pkirole": {"type": "string"}, "prodQualifier": {"type": "string"}, "secOCISCert": {"type": "boolean"}, "serialNo": {"type": "string"}, "services": {"type": "string"}, "signature": {"type": "string"}, "specialECU": {"type": "string"}, "state": {"type": "string", "enum": ["ISSUED", "SIGNING_REQUEST", "VIRTUAL"]}, "subject": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "subjectPublicKey": {"type": "string"}, "targetECU": {"type": "string"}, "targetSubjectKeyIdentifier": {"type": "string"}, "targetVIN": {"type": "string"}, "type": {"type": "string", "enum": ["NO_TYPE", "BACKEND_CA_CERTIFICATE", "BACKEND_CA_LINK_CERTIFICATE", "ROOT_CA_CERTIFICATE", "ROOT_CA_LINK_CERTIFICATE", "ECU_CERTIFICATE", "DIAGNOSTIC_AUTHENTICATION_CERTIFICATE", "ENHANCED_RIGHTS_CERTIFICATE", "TIME_CERTIFICATE", "VARIANT_CODE_USER_CERTIFICATE", "VARIANT_CODING_DEVICE_CERTIFICATE", "SEC_OC_IS", "VIRTUAL_FOLDER"]}, "uniqueECUID": {"type": "string"}, "updateLabel": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "string"}, "validTo": {"type": "string"}, "validityStrengthColor": {"type": "string"}, "version": {"type": "string"}}, "title": "Certificate"}, "CertificateHexFormatOutput": {"type": "object", "properties": {"cArray": {"type": "string"}, "canoe": {"type": "string"}, "configurator5": {"type": "string"}}, "title": "CertificateHexFormatOutput"}, "CertificateReplacementPackageExtendedResult": {"type": "object", "properties": {"backendCertificate": {"$ref": "#/definitions/ExtendedCertificateWithSnSkiResult"}, "ecuCertificate": {"$ref": "#/definitions/ExtendedCertificateWithSnSkiResult"}, "errorMessage": {"type": "string"}, "linkCertificate": {"$ref": "#/definitions/ExtendedCertificateWithSnSkiResult"}, "rootCertificate": {"$ref": "#/definitions/ExtendedCertificateWithSnSkiResult"}, "target": {"type": "string", "enum": ["ECU", "BACKEND", "ROOT"]}}, "title": "CertificateReplacementPackageExtendedResult"}, "CertificateReplacementPackageV3Input": {"type": "object", "required": ["certificate", "targetVIN"], "properties": {"certificate": {"type": "string", "description": "Certificate bytes, Base64 encoded."}, "targetBackendCertSubjKeyId": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}, "uniqueEcuId": {"type": "string", "description": "Unique ECU ID, not base64 encoded. Maximum length is 30 bytes."}}, "title": "CertificateReplacementPackageV3Input"}, "CertificateSummary": {"type": "object", "properties": {"authorityKeyIdentifier": {"type": "string"}, "certificateType": {"type": "string"}, "errorMessage": {"type": "string"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "serialNo": {"type": "string"}, "subject": {"type": "string"}, "targetVIN": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "integer", "format": "int64"}, "validTo": {"type": "integer", "format": "int64"}}, "title": "CertificateSummary"}, "CertificateUserRoleResult": {"type": "object", "properties": {"userRole": {"type": "string", "description": "User Role Text as in specs: Supplier, Development ENHANCED, Production, After-Sales ENHANCED, After-Sales STANDARD, After-Sales BASIC, Internal Diagnostic Test Tool, ePTI Test Tool"}, "userRoleNumber": {"type": "string", "description": "User Role Number as in specs: 1, 2, 3, 4, 5, 6, 7"}}, "title": "CertificateUserRoleResult"}, "CertificatesUseCaseHolder": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "title": "CertificatesUseCaseHolder"}, "CheckOwnershipInput": {"type": "object", "required": ["backendCertSubjKeyId", "ecuChallenge", "serialNumber"], "properties": {"backendCertSubjKeyId": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "ecuChallenge": {"type": "string", "description": "ECU challenge, Base64 encoded"}, "serialNumber": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}}, "title": "CheckOwnershipInput"}, "Configuration": {"type": "object", "properties": {"configKey": {"type": "string"}, "configValue": {"type": "string"}, "createTimestamp": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}}, "title": "Configuration"}, "DeleteCertificateModel": {"type": "object", "required": ["authorityKeyIdentifier", "serialNo"], "properties": {"authorityKeyIdentifier": {"type": "string", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes."}, "serialNo": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}}, "title": "DeleteCertificateModel"}, "EcuSignRequestInput": {"type": "object", "required": ["backendSubjectKeyIdentifier", "challenge", "ecuId"], "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "challenge": {"type": "string", "description": "Challenge byte array to be signed, Base64 encoded."}, "ecuId": {"type": "string", "description": "ECU id"}, "ecuSerialNumber": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}}, "title": "EcuSignRequestInput"}, "Enablement": {"type": "object", "properties": {"enable": {"type": "boolean"}}, "title": "Enablement"}, "EncryptedPKCSImportResult": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "summary": {"type": "string"}, "importResult": {"type": "array", "items": {"$ref": "#/definitions/ImportResult"}}}, "title": "EncryptedPKCSImportResult"}, "EncryptedPKCSPackageInput": {"type": "object", "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "encryptedPKCSPackage": {"type": "string", "description": "Encrypted PKCS package generated by SigModul."}}, "title": "EncryptedPKCSPackageInput"}, "EnhancedRightsItem": {"type": "object", "properties": {"serialNoEnhRightsCert": {"type": "string", "description": "Serial number of the determined enhanced rights certificate - base 64 encoded"}, "serialNoHolder": {"type": "string", "description": "Serial number of the determined holder certificate - base 64 encoded"}, "services": {"type": "string", "description": "All services of the Enhanced Rights certificate as Hex and comma seperated."}}, "title": "EnhancedRightsItem"}, "EnhancedRightsResponse": {"type": "object", "properties": {"backendPartNumber": {"type": "string", "description": "Part number value of backend parent"}, "backendSubjectKeyIdentifier": {"type": "string", "description": "The Backend (base64 encoded)."}, "certificates": {"type": "array", "items": {"$ref": "#/definitions/EnhancedRightsItem"}}, "errorMessage": {"type": "string"}}, "title": "EnhancedRightsResponse"}, "Enumeration": {"type": "object", "title": "Enumeration"}, "ExtendedCertificateResult": {"type": "object", "properties": {"certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "partNumber": {"type": "string", "description": "Part number value"}}, "title": "ExtendedCertificateResult"}, "ExtendedCertificateWithSNAndIssuerSNResult": {"type": "object", "properties": {"backendPartNumber": {"type": "string", "description": "Part number value of backend parent"}, "certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "issuerSerialNumber": {"type": "string", "description": "Issuer serial number encoded bytes in base64 format"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}}, "title": "ExtendedCertificateWithSNAndIssuerSNResult"}, "ExtendedCertificateWithSNAndUserRoleResult": {"type": "object", "properties": {"backendPartNumber": {"type": "string", "description": "Part number value of backend parent"}, "certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}, "userRole": {"type": "string", "description": "User Role Text as in specs: Supplier, Development ENHANCED, Production, After-Sales ENHANCED, After-Sales STANDARD, After-Sales BASIC, Internal Diagnostic Test Tool, ePTI Test Tool"}, "userRoleNumber": {"type": "string", "description": "User Role Number as in specs: 1, 2, 3, 4, 5, 6, 7"}}, "title": "ExtendedCertificateWithSNAndUserRoleResult"}, "ExtendedCertificateWithSNResult": {"type": "object", "properties": {"backendPartNumber": {"type": "string", "description": "Part number value of backend parent"}, "certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}}, "title": "ExtendedCertificateWithSNResult"}, "ExtendedCertificateWithSnSkiResult": {"type": "object", "properties": {"certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "partNumber": {"type": "string", "description": "Part number value"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}, "subjectKeyIdentifier": {"type": "string", "description": "Subject key identifier encoded bytes in base64 format"}}, "title": "ExtendedCertificateWithSnSkiResult"}, "ExtendedDeleteCertificatesResult": {"type": "object", "properties": {"authKeyIdentifier": {"type": "string"}, "backendUpdateLabel": {"type": "string"}, "certificate": {"type": "boolean"}, "certificateId": {"type": "string"}, "certificateType": {"type": "string"}, "message": {"type": "string"}, "partNumber": {"type": "string"}, "publicKey": {"type": "string"}, "serialNo": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "success": {"type": "boolean"}}, "title": "ExtendedDeleteCertificatesResult"}, "ExtendedEcuSignRequestResult": {"type": "object", "properties": {"backendPartNumber": {"type": "string", "description": "Part number value of backend parent"}, "ecuCertificate": {"type": "string"}, "errorMessage": {"type": "string"}, "expirationDate": {"type": "integer", "format": "int64"}, "serialNumber": {"type": "string"}, "signature": {"type": "string"}}, "title": "ExtendedEcuSignRequestResult"}, "ExtendedVariantCoding": {"type": "object", "properties": {"backendPartNumber": {"type": "string", "description": "Part number value of backend parent"}, "errorMessage": {"type": "string"}, "expirationDate": {"type": "integer", "format": "int64", "description": "Expiration date of Variant Coding Certificate"}, "serialNumber": {"type": "string", "description": "Serial number base64 string encoded"}, "signature": {"type": "string", "description": "Signature base64 string encoded"}, "varCodingCertificate": {"type": "string", "description": "Variant coding certificate base64 string encoded"}}, "title": "ExtendedVariantCoding"}, "Extensions": {"type": "object", "properties": {"criticalExtensionOIDs": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}, "encoded": {"type": "string", "format": "byte"}, "extensionOIDs": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}, "nonCriticalExtensionOIDs": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}}, "title": "Extensions"}, "File": {"type": "object", "properties": {"absolute": {"type": "boolean"}, "absoluteFile": {"$ref": "#/definitions/File"}, "absolutePath": {"type": "string"}, "canonicalFile": {"$ref": "#/definitions/File"}, "canonicalPath": {"type": "string"}, "directory": {"type": "boolean"}, "file": {"type": "boolean"}, "freeSpace": {"type": "integer", "format": "int64"}, "hidden": {"type": "boolean"}, "name": {"type": "string"}, "parent": {"type": "string"}, "parentFile": {"$ref": "#/definitions/File"}, "path": {"type": "string"}, "totalSpace": {"type": "integer", "format": "int64"}, "usableSpace": {"type": "integer", "format": "int64"}}, "title": "File"}, "ImportResult": {"type": "object", "properties": {"authorityKeyIdentifier": {"type": "string"}, "fileName": {"type": "string"}, "message": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "success": {"type": "boolean"}}, "title": "ImportResult"}, "InstallationDetails": {"type": "object", "properties": {"property": {"type": "string"}, "value": {"type": "object"}}, "title": "InstallationDetails"}, "LocalImportInput": {"type": "object", "required": ["filePath"], "properties": {"filePath": {"type": "string", "description": "Path of the file to be imported. The file path has to provided with single slashes as separator, e.g. c:/folder/folder.ext - mind: single backslashes can not be used."}, "password": {"type": "string", "description": "The certificate password, if needed."}}, "title": "LocalImportInput"}, "Ownership": {"type": "object", "required": ["ecuProofOfOwnership"], "properties": {"ecuProofOfOwnership": {"type": "string", "description": "base64 encoded proof"}, "errorMessage": {"type": "string"}}, "title": "Ownership"}, "PartNumberMappingResult": {"type": "object", "required": ["partNumber", "ski"], "properties": {"errorMessage": {"type": "string"}, "partNumber": {"type": "string", "description": "The part number."}, "ski": {"type": "string", "description": "The Subject Key Identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes."}}, "title": "PartNumberMappingResult"}, "PerformanceAuditSummary": {"type": "object", "properties": {"className": {"type": "string"}, "correlation_id": {"type": "string"}, "createTimestamp": {"type": "integer", "format": "int64"}, "createUser": {"type": "string"}, "duration": {"type": "integer", "format": "int64"}, "id": {"type": "string"}, "methodName": {"type": "string"}, "projectRevision": {"type": "string"}, "projectVersion": {"type": "string"}, "updateTimestamp": {"type": "string"}, "updateUser": {"type": "string"}, "userName": {"type": "string"}}, "title": "PerformanceAuditSummary"}, "Principal": {"type": "object", "properties": {"name": {"type": "string"}}, "title": "Principal"}, "ProxyInput": {"type": "object", "properties": {"hostname": {"type": "string"}, "port": {"type": "string"}}, "title": "ProxyInput"}, "ProxyOutput": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "hostname": {"type": "string"}, "port": {"type": "string"}}, "title": "ProxyOutput"}, "ProxyTypeInput": {"type": "object", "properties": {"proxyType": {"type": "string"}}, "title": "ProxyTypeInput"}, "ProxyWithType": {"type": "object", "properties": {"proxyInput": {"$ref": "#/definitions/ProxyInput"}, "proxyType": {"$ref": "#/definitions/ProxyTypeInput"}}, "title": "ProxyWithType"}, "PublicKey": {"type": "object", "properties": {"algorithm": {"type": "string"}, "encoded": {"type": "string", "format": "byte"}, "format": {"type": "string"}}, "title": "PublicKey"}, "RDN": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "first": {"$ref": "#/definitions/AttributeTypeAndValue"}, "multiValued": {"type": "boolean"}, "typesAndValues": {"type": "array", "items": {"$ref": "#/definitions/AttributeTypeAndValue"}}}, "title": "RDN"}, "RawData": {"type": "object", "properties": {"attributesCertificateHolder": {"$ref": "#/definitions/X509AttributeCertificateHolder"}, "cert": {"$ref": "#/definitions/X509Certificate"}, "certificate": {"type": "boolean"}, "existing": {"type": "object"}, "originalBytes": {"type": "string", "format": "byte"}}, "title": "RawData"}, "RolePriorityConfiguration": {"type": "object", "properties": {"id": {"type": "string"}, "role": {"type": "string"}}, "title": "RolePriorityConfiguration"}, "RootOrBackendResult": {"type": "object", "properties": {"certificate": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "type": {"type": "string", "description": "Certificate type"}}, "title": "RootOrBackendResult"}, "SecOCISInputV3": {"type": "object", "required": ["backendCertSubjKeyId", "diagCertSerialNumber", "ecuCertificate", "targetECU", "targetVIN"], "properties": {"backendCertSubjKeyId": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "diagCertSerialNumber": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}, "ecuCertificate": {"type": "string", "description": "Certificate bytes, Base64 encoded."}, "targetECU": {"type": "string", "description": "The target ECU. Maximum size of the field is 30 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}}, "title": "SecOCISInputV3"}, "SecureVariantCodingInput": {"type": "object", "required": ["backendSubjectKeyIdentifier", "data"], "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "data": {"type": "string", "description": "Base64 representation of the data to be signed."}, "targetECU": {"type": "string", "description": "The target ECU. Maximum size of the field is 30 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}}, "title": "SecureVariantCodingInput"}, "SignatureCheckHolder": {"type": "object", "required": ["ecuCertificate", "message", "signature"], "properties": {"ecuCertificate": {"type": "string", "description": "Certificate bytes, Base64 encoded."}, "message": {"type": "string", "description": "Base64 representation of the message which needs to be checked."}, "signature": {"type": "string", "description": "Base64 representation of the signature."}}, "title": "SignatureCheckHolder"}, "SystemIntegrityCheckResultWithoutReport": {"type": "object", "properties": {"integrityCheckErrorMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "totalNumberOfCheckedCertificates": {"type": "integer", "format": "int32"}, "totalNumberOfFailedChecks": {"type": "integer", "format": "int32"}}, "title": "SystemIntegrityCheckResultWithoutReport"}, "UpdateCertificateMetrics": {"type": "object", "properties": {"details": {"type": "string"}, "detailsStep": {"type": "string"}, "didFailAllRetries": {"type": "boolean"}, "errorMetrics": {"type": "boolean"}, "metricsAvailable": {"type": "boolean"}, "running": {"type": "boolean"}, "status": {"type": "string"}, "updateCertificatesRetryInfo": {"$ref": "#/definitions/UpdateCertificatesRetryInfo"}}, "title": "UpdateCertificateMetrics"}, "UpdateCertificatesRetryInfo": {"type": "object", "properties": {"currentRetry": {"type": "integer", "format": "int32"}, "endpoint": {"type": "string"}, "maxRetries": {"type": "integer", "format": "int32"}, "nextRetryTime": {"type": "integer", "format": "int32"}, "nextRetryTimestamp": {"type": "integer", "format": "int64"}}, "title": "UpdateCertificatesRetryInfo"}, "UserData": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "userName": {"type": "string"}}, "title": "UserData"}, "UserDetailsWithSession": {"type": "object", "properties": {"authenticationAgainstBackend": {"type": "boolean"}, "checkLocalPassword": {"type": "boolean"}, "defaultUser": {"type": "boolean"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "newUser": {"type": "boolean"}, "remainingSessionSeconds": {"type": "integer", "format": "int64"}, "transitionValid": {"type": "boolean"}, "userName": {"type": "string"}}, "title": "UserDetailsWithSession"}, "UserLoginRequest": {"type": "object", "required": ["userName", "userPassword"], "properties": {"userName": {"type": "string", "description": "The user name. Length between 1 and 7 characters. Must be alpha numeric, non blank."}, "userPassword": {"type": "string", "description": "The user password as base64 encoded UTF-8 string. Length of the undecoded string must be between 9 and 100 characters. Must contain upper case, lower case, digit, and special characters."}}, "title": "UserLoginRequest"}, "Version": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "build": {"type": "string"}, "serverVersion": {"type": "string"}, "system": {"type": "string"}}, "title": "Version"}, "Versioned": {"type": "object", "title": "Versioned"}, "X500Name": {"type": "object", "properties": {"attributeTypes": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}, "encoded": {"type": "string", "format": "byte"}, "rdns": {"type": "array", "items": {"$ref": "#/definitions/RDN"}}}, "title": "X500Name"}, "X500Principal": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "name": {"type": "string"}}, "title": "X500Principal"}, "X509AttributeCertificateHolder": {"type": "object", "properties": {"attributes": {"type": "array", "items": {"$ref": "#/definitions/Attribute"}}, "criticalExtensionOIDs": {"type": "array", "items": {"type": "object"}}, "encoded": {"type": "string", "format": "byte"}, "extensionOIDs": {"type": "array", "items": {"type": "object"}}, "extensions": {"$ref": "#/definitions/Extensions"}, "holder": {"$ref": "#/definitions/AttributeCertificateHolder"}, "issuer": {"$ref": "#/definitions/AttributeCertificateIssuer"}, "issuerUniqueID": {"type": "array", "items": {"type": "boolean"}}, "nonCriticalExtensionOIDs": {"type": "array", "items": {"type": "object"}}, "notAfter": {"type": "string", "format": "date-time"}, "notBefore": {"type": "string", "format": "date-time"}, "serialNumber": {"type": "integer"}, "signature": {"type": "string", "format": "byte"}, "signatureAlgorithm": {"$ref": "#/definitions/AlgorithmIdentifier"}, "version": {"type": "integer", "format": "int32"}}, "title": "X509AttributeCertificateHolder"}, "X509Certificate": {"type": "object", "properties": {"basicConstraints": {"type": "integer", "format": "int32"}, "criticalExtensionOIDs": {"type": "array", "items": {"type": "string"}}, "encoded": {"type": "string", "format": "byte"}, "extendedKeyUsage": {"type": "array", "items": {"type": "string"}}, "issuerAlternativeNames": {"type": "array", "items": {"type": "array", "items": {"type": "object"}}}, "issuerDN": {"$ref": "#/definitions/Principal"}, "issuerUniqueID": {"type": "array", "items": {"type": "boolean"}}, "issuerX500Principal": {"$ref": "#/definitions/X500Principal"}, "keyUsage": {"type": "array", "items": {"type": "boolean"}}, "nonCriticalExtensionOIDs": {"type": "array", "items": {"type": "string"}}, "notAfter": {"type": "string", "format": "date-time"}, "notBefore": {"type": "string", "format": "date-time"}, "publicKey": {"$ref": "#/definitions/PublicKey"}, "serialNumber": {"type": "integer"}, "sigAlgName": {"type": "string"}, "sigAlgOID": {"type": "string"}, "sigAlgParams": {"type": "string", "format": "byte"}, "signature": {"type": "string", "format": "byte"}, "subjectAlternativeNames": {"type": "array", "items": {"type": "array", "items": {"type": "object"}}}, "subjectDN": {"$ref": "#/definitions/Principal"}, "subjectUniqueID": {"type": "array", "items": {"type": "boolean"}}, "subjectX500Principal": {"$ref": "#/definitions/X500Principal"}, "tbscertificate": {"type": "string", "format": "byte"}, "type": {"type": "string"}, "version": {"type": "integer", "format": "int32"}}, "title": "X509Certificate"}, "ZenZefiCertificate": {"type": "object", "properties": {"activeForTesting": {"type": "boolean"}, "algorithmIdentifier": {"type": "string"}, "authorityKeyIdentifier": {"type": "string"}, "baseCertificateID": {"type": "string"}, "basicConstraints": {"type": "string"}, "basicConstraintsText": {"type": "string"}, "description": {"type": "string"}, "forVsmSimulation": {"type": "boolean"}, "hasChildren": {"type": "boolean"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "issuerSerialNumber": {"type": "string"}, "keyUsage": {"type": "array", "items": {"type": "boolean"}}, "keyUsageText": {"type": "string"}, "nonce": {"type": "string"}, "parentId": {"type": "string"}, "partNumber": {"type": "string"}, "pkiKnown": {"type": "boolean"}, "pkirole": {"type": "string"}, "prodQualifier": {"type": "string"}, "secOCISCert": {"type": "boolean"}, "serialNo": {"type": "string"}, "services": {"type": "string"}, "signature": {"type": "string"}, "specialECU": {"type": "string"}, "state": {"type": "string", "enum": ["ISSUED", "SIGNING_REQUEST", "VIRTUAL"]}, "status": {"type": "string"}, "subject": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "subjectPublicKey": {"type": "string"}, "targetECU": {"type": "string"}, "targetSubjectKeyIdentifier": {"type": "string"}, "targetVIN": {"type": "string"}, "type": {"type": "string", "enum": ["NO_TYPE", "BACKEND_CA_CERTIFICATE", "BACKEND_CA_LINK_CERTIFICATE", "ROOT_CA_CERTIFICATE", "ROOT_CA_LINK_CERTIFICATE", "ECU_CERTIFICATE", "DIAGNOSTIC_AUTHENTICATION_CERTIFICATE", "ENHANCED_RIGHTS_CERTIFICATE", "TIME_CERTIFICATE", "VARIANT_CODE_USER_CERTIFICATE", "VARIANT_CODING_DEVICE_CERTIFICATE", "SEC_OC_IS", "VIRTUAL_FOLDER"]}, "uniqueECUID": {"type": "string"}, "updateLabel": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "string"}, "validTo": {"type": "string"}, "validToDateTime": {"type": "string"}, "validityStrengthColor": {"type": "string"}, "version": {"type": "string"}}, "title": "ZenZefiCertificate"}, "ZenZefiCertificateSummary": {"type": "object", "properties": {"authorityKeyIdentifier": {"type": "string"}, "certificateType": {"type": "string"}, "errorMessage": {"type": "string"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "nonce": {"type": "string"}, "partNumber": {"type": "string"}, "serialNo": {"type": "string"}, "status": {"type": "string"}, "subject": {"type": "string"}, "targetECU": {"type": "string"}, "targetVIN": {"type": "string"}, "updateLabel": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "integer", "format": "int64"}, "validTo": {"type": "integer", "format": "int64"}}, "title": "ZenZefiCertificateSummary"}, "ZenZefiCreateSignatureInput": {"type": "object", "properties": {"codingData": {"type": "string", "description": "Base64 representation of the coding data to be signed."}, "targetECU": {"type": "string", "description": "The target ECU. Maximum size of the field is 30 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}}, "title": "ZenZefiCreateSignatureInput"}, "ZenZefiCreateSignatureResult": {"type": "object", "required": ["backendSubjectKeyIdentifier"], "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "Backend Certificate Subject Key Identifier encoded bytes in base64 format"}, "certificateData": {"type": "string", "description": "Variant Coding User Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "serialNumber": {"type": "string", "description": "Variant Coding User Certificate Serial Number encoded bytes in base64 format"}, "signature": {"type": "string", "description": "Signed data encoded bytes in base64 format"}, "validTo": {"type": "integer", "format": "int64", "description": "Variant Coding User Certificate expiration timestamp"}}, "title": "ZenZefiCreateSignatureResult"}, "ZenZefiUser": {"type": "object", "required": ["firstName", "lastName", "organisation", "userName"], "properties": {"createTimestamp": {"type": "string"}, "firstName": {"type": "string", "description": "The first name. Length between 1 and 100 characters."}, "id": {"type": "string"}, "lastName": {"type": "string", "description": "The last name. Length between 1 and 100 characters."}, "organisation": {"type": "string", "description": "The organisation. Length between 1 and 100 characters."}, "role": {"type": "string", "enum": ["DEFAULT", "REGULAR"]}, "userName": {"type": "string", "description": "The user name. Length between 1 and 7 characters. Must be alpha numeric, non blank."}, "userPassword": {"type": "string"}}, "title": "ZenZefiUser"}}}