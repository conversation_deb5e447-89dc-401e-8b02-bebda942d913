PeriodFormat.space=\ 
PeriodFormat.comma=,
PeriodFormat.commandand=,a 
PeriodFormat.commaspaceand=, a 
PeriodFormat.commaspace=, 
PeriodFormat.spaceandspace=\ a 
PeriodFormat.regex.separator=%
PeriodFormat.years.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.years.list=\ rok%\ roky%\ let
PeriodFormat.months.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.months.list=\ m\u011Bs\u00EDc%\ m\u011Bs\u00EDce%\ m\u011Bs\u00EDc\u016F
PeriodFormat.weeks.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.weeks.list=\ t\u00FDden%\ t\u00FDdny%\ t\u00FDdn\u016F
PeriodFormat.days.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.days.list=\ den%\ dny%\ dn\u016F
PeriodFormat.hours.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.hours.list=\ hodina%\ hodiny%\ hodin
PeriodFormat.minutes.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.minutes.list=\ minuta%\ minuty%\ minut
PeriodFormat.seconds.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.seconds.list=\ sekunda%\ sekundy%\ sekund
PeriodFormat.milliseconds.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.milliseconds.list=\ milisekunda%\ milisekundy%\ milisekund
