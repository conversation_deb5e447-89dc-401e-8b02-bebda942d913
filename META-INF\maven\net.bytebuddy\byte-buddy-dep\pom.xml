<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-parent</artifactId>
        <version>1.10.8</version>
    </parent>

    <artifactId>byte-buddy-dep</artifactId>
    <packaging>jar</packaging>

    <name>Byte Buddy (with dependencies)</name>
    <description>
        Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with a remaining dependency onto ASM.
        You should never depend on this module without repackaging Byte Buddy and ASM into your own namespace.
    </description>

    <dependencies>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>${version.asm}</version>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm-commons</artifactId>
            <version>${version.asm}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm-analysis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm-tree</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${version.junit}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${version.mockito}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy-agent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm-util</artifactId>
            <version>${version.asm}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm-analysis</artifactId>
            <version>${version.asm}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>..</directory>
                <targetPath>META-INF</targetPath>
                <filtering>true</filtering>
                <includes>
                    <include>LICENSE</include>
                    <include>NOTICE</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!-- Create hashCode/equals method and cache some expensive properties. -->
            <plugin>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-maven-plugin</artifactId>
                <version>1.10.7</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>transform</goal>
                        </goals>
                        <configuration>
                            <initialization>
                                <entryPoint>REDEFINE</entryPoint>
                            </initialization>
                            <transformations>
                                <transformation>
                                    <groupId>net.bytebuddy</groupId>
                                    <artifactId>byte-buddy</artifactId>
                                    <version>1.10.7</version>
                                    <plugin>net.bytebuddy.build.HashCodeAndEqualsPlugin$WithNonNullableFields</plugin>
                                </transformation>
                                <transformation>
                                    <groupId>net.bytebuddy</groupId>
                                    <artifactId>byte-buddy</artifactId>
                                    <version>1.10.7</version>
                                    <plugin>net.bytebuddy.build.CachedReturnPlugin</plugin>
                                </transformation>
                            </transformations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
