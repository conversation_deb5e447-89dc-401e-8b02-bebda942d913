<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.daimler.cebas</groupId>
		<artifactId>zenzefi_pom</artifactId>
		<version>*********</version>
		<relativePath>../zenzefi-parent-pom/zenzefi_pom.xml</relativePath>
	</parent>

	<groupId>com.daimler.cebas</groupId>
	<artifactId>zenzefi-client</artifactId>
	<version>*********</version>
	<description>ZenZefi Client</description>

	<properties>
		<swagger.file>${project.basedir}/src/main/resources/zenzefi_swagger_generated.json</swagger.file>
		<swaggerV1.file>${project.basedir}/src/main/resources/zenzefi_swagger_generatedV1.json</swaggerV1.file>
		<swaggerV2.file>${project.basedir}/src/main/resources/zenzefi_swagger_generatedV2.json</swaggerV2.file>
		<swaggerV3.file>${project.basedir}/src/main/resources/zenzefi_swagger_generatedV3.json</swaggerV3.file>
		<default.package>com.daimler.cebas.zenzefi.client</default.package>
	</properties>

	<dependencies>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp</groupId>
			<artifactId>logging-interceptor</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<!-- test dependencies -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>javax.annotation-api</artifactId>
		</dependency>
	</dependencies>

	<build>
		<finalName>zenzefi_client</finalName>
		<plugins>
			<plugin>
				<groupId>io.swagger</groupId>
				<artifactId>swagger-codegen-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>newestReleasedApi</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<inputSpec>${swagger.file}</inputSpec>
							<language>java</language>
							<output>${project.build.directory}/generated-sources</output>
							<invokerPackage>${default.package}</invokerPackage>
							<apiPackage>${default.package}.swagger.api</apiPackage>
							<modelPackage>${default.package}.model</modelPackage>
							<configOptions>
								<generateApiTests>false</generateApiTests>
								<generateModelTests>false</generateModelTests>
							</configOptions>
						</configuration>
					</execution>

					<execution>
						<id>apiV1</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<inputSpec>${swaggerV1.file}</inputSpec>
							<language>java</language>
							<output>${project.build.directory}/generated-sources</output>
							<invokerPackage>${default.package}</invokerPackage>
							<apiPackage>${default.package}.swagger.api.v1</apiPackage>
							<modelPackage>${default.package}.modelV1</modelPackage>
							<configOptions>
								<generateApiTests>false</generateApiTests>
								<generateModelTests>false</generateModelTests>
							</configOptions>
						</configuration>
					</execution>

					<execution>
						<id>apiV2</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<inputSpec>${swaggerV2.file}</inputSpec>
							<language>java</language>
							<output>${project.build.directory}/generated-sources</output>
							<invokerPackage>${default.package}</invokerPackage>
							<apiPackage>${default.package}.swagger.api.v2</apiPackage>
							<modelPackage>${default.package}.modelV2</modelPackage>
							<configOptions>
								<generateApiTests>false</generateApiTests>
								<generateModelTests>false</generateModelTests>
							</configOptions>
						</configuration>
					</execution>
					
					<execution>
						<id>apiV3</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<inputSpec>${swaggerV3.file}</inputSpec>
							<language>java</language>
							<output>${project.build.directory}/generated-sources</output>
							<invokerPackage>${default.package}</invokerPackage>
							<apiPackage>${default.package}.swagger.api.v3</apiPackage>
							<modelPackage>${default.package}.modelV3</modelPackage>
							<configOptions>
								<generateApiTests>false</generateApiTests>
								<generateModelTests>false</generateModelTests>
							</configOptions>
						</configuration>
					</execution>

				</executions>
			</plugin>



			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<!-- Run shade goal on package phase -->
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
