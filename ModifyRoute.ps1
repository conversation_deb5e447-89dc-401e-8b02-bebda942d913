# Route.class dosyasını oku
$classPath = "com/squareup/okhttp/Route.class"
$bytes = [System.IO.File]::ReadAllBytes($classPath)

# IP adresini değiştir: ************ -> *************
$oldIP = [System.Text.Encoding]::UTF8.GetBytes("************")
$newIP = [System.Text.Encoding]::UTF8.GetBytes("*************")

# IP adresinin pozisyonunu bul
$ipPosition = -1
for ($i = 0; $i -le ($bytes.Length - $oldIP.Length); $i++) {
    $match = $true
    for ($j = 0; $j -lt $oldIP.Length; $j++) {
        if ($bytes[$i + $j] -ne $oldIP[$j]) {
            $match = $false
            break
        }
    }
    if ($match) {
        $ipPosition = $i
        break
    }
}

if ($ipPosition -ne -1) {
    Write-Host "IP adresi bulundu pozisyon: $ipPosition"
    
    # Yeni IP adresini yerleştir
    for ($i = 0; $i -lt $newIP.Length; $i++) {
        $bytes[$ipPosition + $i] = $newIP[$i]
    }
    
    # Eğer yeni IP daha kısaysa, kalan kısmı null byte'larla doldur
    if ($newIP.Length -lt $oldIP.Length) {
        for ($i = $newIP.Length; $i -lt $oldIP.Length; $i++) {
            $bytes[$ipPosition + $i] = 0
        }
    }
    
    Write-Host "IP adresi değiştirildi: ************ -> *************"
} else {
    Write-Host "IP adresi bulunamadı!"
}

# Port değerini değiştir: 58000 (0xE290) -> 80 (0x0050)
# Java'da int constant pool'da big-endian formatında saklanır
$oldPortBytes = @(0x00, 0x00, 0xE2, 0x90)  # 58000
$newPortBytes = @(0x00, 0x00, 0x00, 0x50)  # 80

$portPosition = -1
for ($i = 0; $i -le ($bytes.Length - 4); $i++) {
    if ($bytes[$i] -eq $oldPortBytes[0] -and 
        $bytes[$i+1] -eq $oldPortBytes[1] -and 
        $bytes[$i+2] -eq $oldPortBytes[2] -and 
        $bytes[$i+3] -eq $oldPortBytes[3]) {
        $portPosition = $i
        break
    }
}

if ($portPosition -ne -1) {
    Write-Host "Port değeri bulundu pozisyon: $portPosition"
    
    # Yeni port değerini yerleştir
    for ($i = 0; $i -lt 4; $i++) {
        $bytes[$portPosition + $i] = $newPortBytes[$i]
    }
    
    Write-Host "Port değeri değiştirildi: 58000 -> 80"
} else {
    Write-Host "Port değeri bulunamadı!"
}

# Değiştirilmiş dosyayı kaydet
[System.IO.File]::WriteAllBytes($classPath, $bytes)
Write-Host "Route.class dosyası başarıyla güncellendi!"
