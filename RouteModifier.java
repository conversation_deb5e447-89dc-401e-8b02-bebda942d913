import java.io.*;
import java.nio.file.*;

public class RouteModifier {
    public static void main(String[] args) throws IOException {
        // Route.class dosyasını oku
        byte[] classBytes = Files.readAllBytes(Paths.get("com/squareup/okhttp/Route.class"));
        
        // Eski IP adresini yeni IP adresi ile değiştir
        String oldIP = "************";
        String newIP = "*************";
        
        // String'leri byte array'e çevir
        byte[] oldIPBytes = oldIP.getBytes();
        byte[] newIPBytes = newIP.getBytes();
        
        // IP adresini değiştir
        classBytes = replaceBytes(classBytes, oldIPBytes, newIPBytes);
        
        // Port değerini değiştir (58000 -> 80)
        // 58000 = 0xE290, 80 = 0x0050
        byte[] oldPort = {(byte)0x00, (byte)0x00, (byte)0xE2, (byte)0x90};
        byte[] newPort = {(byte)0x00, (byte)0x00, (byte)0x00, (byte)0x50};
        
        classBytes = replaceBytes(classBytes, oldPort, newPort);
        
        // Değiştirilmiş class dosyasını kaydet
        Files.write(Paths.get("com/squareup/okhttp/Route.class"), classBytes);
        
        System.out.println("Route.class başarıyla güncellendi!");
        System.out.println("IP: " + oldIP + " -> " + newIP);
        System.out.println("Port: 58000 -> 80");
    }
    
    private static byte[] replaceBytes(byte[] source, byte[] target, byte[] replacement) {
        if (target.length != replacement.length) {
            // Eğer uzunluklar farklıysa, padding ekle
            if (replacement.length < target.length) {
                byte[] paddedReplacement = new byte[target.length];
                System.arraycopy(replacement, 0, paddedReplacement, 0, replacement.length);
                // Kalan kısmı null byte'larla doldur
                for (int i = replacement.length; i < target.length; i++) {
                    paddedReplacement[i] = 0;
                }
                replacement = paddedReplacement;
            }
        }
        
        // Target pattern'i bul ve değiştir
        for (int i = 0; i <= source.length - target.length; i++) {
            boolean found = true;
            for (int j = 0; j < target.length; j++) {
                if (source[i + j] != target[j]) {
                    found = false;
                    break;
                }
            }
            if (found) {
                // Pattern bulundu, değiştir
                System.arraycopy(replacement, 0, source, i, replacement.length);
                break;
            }
        }
        
        return source;
    }
}
