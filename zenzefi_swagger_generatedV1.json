{"swagger": "2.0", "info": {"description": "This version is deprecated. Please switch to the newest version <span style='color:red'>(v3)</span> as soon as possible.\nZenZefi REST API V1 provides operations for resources certificate, configuration, log, user, system.", "version": "1.0", "title": "ZenZefi REST API"}, "host": "localhost:61000", "basePath": "/", "tags": [{"name": "ZenZefi Public API V1", "description": "Exposed endpoints for external clients"}], "paths": {"/v1/certificates": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Returns an array of certificates belonging to the current logged in user.", "operationId": "allV1UsingGET", "produces": ["*/*", "application/json"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCertificate"}}}, "500": {"description": "Internal server error"}}, "deprecated": true}, "delete": {"tags": ["ZenZefi Public API V1"], "summary": "Deletes all certificates from current user-specific certificate store, or by authority key identifier and serial number. For details about the parameters, please view the request model.", "operationId": "deleteCertificatesV1", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "all", "in": "query", "description": "If set to true, deletes all certificates.", "required": false, "type": "boolean", "default": false, "allowEmptyValue": false, "x-example": false}, {"in": "body", "name": "pairs", "description": "Base64 encoded authority key identifier / serial number pairs.", "required": false, "schema": {"type": "array", "items": {"$ref": "#/definitions/DeleteCertificateModel"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/DeleteCertificatesResult"}}}, "400": {"description": "Empty input list. | Invalid authority key identifier. The length is 20 bytes. | The input is not Base64 encoded. | Invalid serial number. The max length is 16 bytes. | The certificate with id does not exist in current user-specific certificate store."}, "404": {"description": "Delete certificates called but no match was found."}}, "deprecated": true}}, "/v1/certificates/checkSystemIntegrity": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Makes system integrity check and downloads the report. Endpoint used only in the ZenZefi CLI.", "operationId": "checkSystemIntegrityV1", "produces": ["*/*", "application/xml"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}, "deprecated": true}}, "/v1/certificates/checkSystemIntegrityLog": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Returns the system integrity check XML report. Endpoint used only in the ZenZefi UI.", "operationId": "getIntegrityCheckLogFileV1", "produces": ["application/xml"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/File"}}, "400": {"description": "System integrity check xml report not found"}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/diagCertForCentralAuthentication": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Diagnostic Authentication certificates for central authentication are regular Diagnostic Authentication certificates, which however must not be restricted to a specific ECU and which must not contain certain user roles (individually configurable)", "operationId": "diagCertForCentralAuthenticationV1", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateWithSNAndUserRoleResult"}}}, "deprecated": true}}, "/v1/certificates/import/fromLocal": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Import certificates into current user-specific certificate store. For this endpoint the inputs are the certificate or PKCS12 file path, and password in the case of PKCS12.", "operationId": "importCertificatesFromPathsV1UsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "importCertificatesFromPathsV1", "description": "File path / password pairs. The file path has to provided with single slashes as separator, e.g. c:/folder/folder.ext - mind: single backslashes can not be used.", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/LocalImportInput"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ImportResult"}}}, "400": {"description": "Cannot read the file from path"}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": true}}, "/v1/certificates/importProductionCerts": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Import encrypted PKCS#12 packages. The packages are exported from SigModul, and encrypted with the public key generated by ZenZefi. The packages are Base64 encoded. For details about the parameters, please view the request model.", "operationId": "importEncryptedPKCSPackageV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "pkcsInput", "description": "Base64 encoded backend subject key identifier, encrypted PKCS package generated by SigModul.", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/EncryptedPKCSPackageInput"}}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/EncryptedPKCSImportResult"}}, "400": {"description": "User key pair for production not found. | Failed to decode the backend subject key identifier. | Failed to decode PKCS package for backend with subject key identifier. | Decryption of Certificate Package failed for backend with subject key identifier."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": true}}, "/v1/certificates/list": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Returns an array of certificates belonging to the current logged in user.", "operationId": "listCertificatesV1", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCertificateSummary"}}}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/restore": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Restore root and backend certificates from application binaries.", "operationId": "restoreCertificatesV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "406": {"description": "Operation not allowed while certificates update process is active."}, "500": {"description": "Restoring certificates failed"}}, "deprecated": true}}, "/v1/certificates/search": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Retrieve certificate by authority key identifier and serial number.", "operationId": "getCertByIDV1", "produces": ["*/*"], "parameters": [{"name": "AuthorityKeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "SerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateResult"}}, "404": {"description": "No certificate was found with given authority key identifier and serial number."}}, "deprecated": true}}, "/v1/certificates/search/certificateReplacementPackage": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Get certificate replacement package based on given criteria. For details about the parameters, please view the request model.", "operationId": "certificateReplacementPackageV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "input", "description": "Base64 encoded certificate bytes, replacement target and target backend subject key identifier.", "required": true, "schema": {"$ref": "#/definitions/CertificateReplacementPackageV1Input"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateReplacementPackageV1Result"}}, "404": {"description": "Certificate replacement package not found. | No Certificate was found matching the filter criteria."}}, "deprecated": true}}, "/v1/certificates/search/diag": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Get Diagnostic Certificate based on given criteria.", "operationId": "diagCertV1", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "UserRole", "in": "query", "description": "The role of the user: 1=Supplier, 2=Development ENHANCED, 3=Production, 4=After-Sales ENHANCED, 5=After-Sales STANDARD, 6=After-Sales BASIC, 7=Internal Diagnostic Test Tool, 8=ePTI Test Tool", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateWithSNResult"}}, "400": {"description": "Invalid subject key identifier. The length is 20 bytes. | Invalid target VIN. The length is 17 bytes. | Invalid target ECU. The max length is 30 bytes."}, "404": {"description": "No Certificate was found matching the filter criteria."}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/search/diag/active": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Checks whether a call to get diagnosis certificate will return the certificate with the serial number provided as parameter.", "operationId": "checkActiveDiagCertV1", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "DiagCertSerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "UserRole", "in": "query", "description": "The role of the user: 1=Supplier, 2=Development ENHANCED, 3=Production, 4=After-Sales ENHANCED, 5=After-Sales STANDARD, 6=After-Sales BASIC, 7=Internal Diagnostic Test Tool, 8=ePTI Test Tool", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "400": {"description": "Invalid subject key identifier. The length is 20 bytes. | Invalid serial number. The max length is 16 bytes. | Invalid target VIN. The length is 17 bytes. | Invalid target ECU. The max length is 30 bytes."}, "404": {"description": "No Certificate was found matching the filter criteria."}}, "deprecated": true}}, "/v1/certificates/search/diag/checkOwnership": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Searches for a certificate with the backend subject key identifier and serial number from the request and calculates the signature for ECUChallenge. For details about the parameters, please view the request model.", "operationId": "checkOwnershipV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "checkOwnershipInput", "description": "Base64 encoded backend subject key identifier, Base64 encoded ecu challenge, Base64 encoded serial number.", "required": true, "schema": {"$ref": "#/definitions/CheckOwnershipInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Ownership"}}, "400": {"description": "CheckOwnership validation failed."}, "404": {"description": "No certificate with given serial number found."}, "409": {"description": "More than one certificate found"}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/search/enhRights": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Retrieve an enhanced rights certificate based on given criteria.", "operationId": "enhRightsV1", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "DiagCertSerialNumber", "in": "query", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CertificateWithSNAndIssuerSNResult"}}}, "404": {"description": "No Certificate was found matching the filter criteria.", "schema": {"$ref": "#/definitions/CEBASResult"}}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/search/secOCISCert": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Retrieve a Sec OCIS certificate based on given criteria. For details about the parameters, please view the request model.", "operationId": "secOCISCertV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "secOCISInput", "description": "Base64 encoded backend subject key identifier, Base64 encoded diag serial number, Base64 encoded ECU certificate, target ECU, target VIN.", "required": true, "schema": {"$ref": "#/definitions/SecOCISInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateWithSNResult"}}, "404": {"description": "Could not find diagnostic certificate."}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/search/secureVariantCoding": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Retrieve a variant coding user certificate based on given criteria. For details about the parameters, please view the request model.", "operationId": "secureVariantCodingV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "secureVariantCodingInput", "description": "Base64 encoded backend subject key identifier, Base64 representation of the data to be signed, target ECU, target VIN.", "required": true, "schema": {"$ref": "#/definitions/SecureVariantCodingInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/VariantCoding"}}, "400": {"description": "Signing Coding String failed"}, "404": {"description": "No Certificate was found matching the filter criteria."}}, "deprecated": true}}, "/v1/certificates/search/timeCert": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Retrieve a time certificate based on given criteria.", "operationId": "timeCertV1", "produces": ["*/*"], "parameters": [{"name": "BackendCert-Subject-KeyIdentifier", "in": "query", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "<PERSON><PERSON>", "in": "query", "description": "The nonce. Must be Base64 encoded and have the length of 32 bytes.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "TargetECU", "in": "query", "description": "The target ECU. Maximum size of the field is 30 bytes.", "required": false, "type": "string", "allowEmptyValue": false}, {"name": "TargetVIN", "in": "query", "description": "The target VIN. The size of the field is 17 characters.", "required": true, "type": "string", "allowEmptyValue": false}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CertificateWithSNResult"}}, "400": {"description": "Invalid format for the given ID. | Invalid subject key identifier. The length is 20 bytes. | Invalid authority key identifier. The length is 20 bytes. | Invalid nonce. The length is 32 bytes. | The input is not Base64 encoded. | Invalid target ECU. The max length is 30 bytes. | Invalid target VIN. The length is 17 bytes."}, "404": {"description": "No certificate found. | No certificate found for backend subject key identifier when creating CSR."}, "406": {"description": "Parent Certificate type is invalid! Only Backend Root and Diagnostic Authentication Certificate supported"}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/certificates/signECU": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Signs a challenge byte array with the private key of an ECU certificate to prove the ownership of the certificate.", "operationId": "signEcuRequestV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "input", "description": "ECU sign request input.", "required": true, "schema": {"$ref": "#/definitions/EcuSignRequestInput"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/EcuSignRequestResult"}}, "400": {"description": "The input is not Base64 encoded. | Backend subject key identifier is mandatory. | Challenge byte array is mandatory. | Invalid Unique ECU ID. | Certificate with authority key identifier and unique ECU ID not found. | No key pair found for certificate with ID. Certificate with authority key identifier and serialNo not found."}}, "deprecated": true}}, "/v1/certificates/signatureCheck": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Checks message signature, signed by the input certificate. For details about the parameters, please view the request model.", "operationId": "signatureCheckV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "signatureCheckModel", "description": "Base64 encoded ECU certificate, Base64 encoded message, Base64 encoded signature.", "required": true, "schema": {"$ref": "#/definitions/SignatureCheckHolder"}}], "responses": {"200": {"description": "Signature check succeeded.", "schema": {"type": "boolean"}}, "400": {"description": "Could not verify signature for message. | Wrong input. All parameters are mandatory."}, "404": {"description": "Could not create certificate from bytes input stream. | Could not check signature of the message for an enhanced rights certificate, no public key."}, "500": {"description": "Signature check failed."}}, "deprecated": true}}, "/v1/certificates/signatures": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Sign coding data for all backends with available Variant Coding User certificates.", "operationId": "signaturesV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "createSignatureInput", "description": "Base64 encoded coding data, ecu, vin", "required": true, "schema": {"$ref": "#/definitions/ZenZefiCreateSignatureInput"}}], "responses": {"200": {"description": "Sign request successfully processed.", "schema": {"type": "array", "items": {"$ref": "#/definitions/ZenZefiCreateSignatureResult"}}}, "500": {"description": "Sign request couldn't be processed."}}, "deprecated": true}}, "/v1/certificates/update/differential": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Perform differential update of the certificates. Only those certificates in the user-specific certificate store.", "operationId": "updateDifferentialCertificatesV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Registered user needs to be logged in."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": true}}, "/v1/certificates/update/full": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Perform full update of the certificates. All certificates the user has rights for are requested independently of the certificate renewal time.", "operationId": "updateFullCertificatesV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Registered user needs to be logged in."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": true}}, "/v1/certificates/update/metrics": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Returns the metrics during the certificates update process", "operationId": "updateCertificatesMetricsV1", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Client-ID", "in": "header", "description": "Client id which identifies the client that makes a request to the server.", "required": false, "type": "string"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UpdateCertificateMetrics"}}}, "deprecated": true}}, "/v1/certificates/{ids}": {"delete": {"tags": ["ZenZefi Public API V1"], "summary": "Deletes one or more certificates from current user-specific certificate store. The deletion is made by certificate ids, and each certificate id must be a valid UUID. The ids are comma separated.", "operationId": "deleteCertificatesUsingIdsV1", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"name": "ids", "in": "path", "description": "The ids of the certificates, comma separated. Must be valid UUIDs.", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/DeleteCertificatesResult"}}}, "400": {"description": "Invalid format for the given ID. | Delete certificates called but no match was found. | The certificate with id does not exist in current user-specific certificate store."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": true}}, "/v1/logs/getLogFile": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Get file with logs created since the last call. First call will return all available log entries.", "operationId": "getLogFileV1", "produces": ["application/xml"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/File"}}, "500": {"description": "Download log file failed."}}, "deprecated": true}}, "/v1/system/generateKeyPair": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Key Pair generation. Used for communication with other systems. SigModul uses the generated public key to encrypt PKCS packages.", "operationId": "generateKeyPairv1UsingGET", "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "The public key base64 encoded.", "schema": {"type": "string"}}, "401": {"description": "Unauthorized operation 'generateKeyPair' for registered user."}, "406": {"description": "Operation not allowed while certificates update process is active."}}, "deprecated": true}}, "/v1/system/version": {"get": {"tags": ["ZenZefi Public API V1"], "summary": "Gets ZenZefi server version and api version.", "operationId": "getVersionV1UsingGET", "produces": ["*/*", "application/json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Version"}}, "500": {"description": "Internal server error"}}, "deprecated": true}}, "/v1/users/login": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Local login for an exiting user in based on the provided username and password.", "operationId": "loginV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}, {"in": "body", "name": "user", "description": "The user.", "required": true, "schema": {"$ref": "#/definitions/UserLoginRequest"}}], "responses": {"200": {"description": "Login successful.", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "406": {"description": "Login error."}, "412": {"description": "User already logged in."}, "500": {"description": "Exception message"}}, "deprecated": true}}, "/v1/users/logout": {"post": {"tags": ["ZenZefi Public API V1"], "summary": "Log out a user from ZenZefi.", "operationId": "logoutV1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Locale", "in": "header", "description": "The language for the operation. E.g. en, de", "required": false, "type": "string", "default": "en"}, {"name": "X-Correlation-ID", "in": "header", "description": "Business id which identifies the request and business case.", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "deprecated": true}}}, "definitions": {"ASN1Encodable": {"type": "object", "title": "ASN1Encodable"}, "ASN1ObjectIdentifier": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "id": {"type": "string"}}, "title": "ASN1ObjectIdentifier"}, "ASN1Set": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "objects": {"$ref": "#/definitions/Enumeration"}}, "title": "ASN1Set"}, "AlgorithmIdentifier": {"type": "object", "properties": {"algorithm": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "encoded": {"type": "string", "format": "byte"}, "parameters": {"$ref": "#/definitions/ASN1Encodable"}}, "title": "AlgorithmIdentifier"}, "Attribute": {"type": "object", "properties": {"attrType": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "attrValues": {"$ref": "#/definitions/ASN1Set"}, "attributeValues": {"type": "array", "items": {"$ref": "#/definitions/ASN1Encodable"}}, "encoded": {"type": "string", "format": "byte"}}, "title": "Attribute"}, "AttributeCertificateHolder": {"type": "object", "properties": {"digestAlgorithm": {"$ref": "#/definitions/AlgorithmIdentifier"}, "digestedObjectType": {"type": "integer", "format": "int32"}, "entityNames": {"type": "array", "items": {"$ref": "#/definitions/X500Name"}}, "issuer": {"type": "array", "items": {"$ref": "#/definitions/X500Name"}}, "objectDigest": {"type": "string", "format": "byte"}, "otherObjectTypeID": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "serialNumber": {"type": "integer"}}, "title": "AttributeCertificateHolder"}, "AttributeCertificateIssuer": {"type": "object", "properties": {"names": {"type": "array", "items": {"$ref": "#/definitions/X500Name"}}}, "title": "AttributeCertificate<PERSON>ssuer"}, "AttributeTypeAndValue": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "type": {"$ref": "#/definitions/ASN1ObjectIdentifier"}, "value": {"$ref": "#/definitions/ASN1Encodable"}}, "title": "AttributeTypeAndValue"}, "CEBASResult": {"type": "object", "properties": {"errorMessage": {"type": "string"}}, "title": "CEBASResult"}, "Certificate": {"type": "object", "properties": {"algorithmIdentifier": {"type": "string"}, "authorityKeyIdentifier": {"type": "string"}, "baseCertificateID": {"type": "string"}, "basicConstraints": {"type": "string"}, "basicConstraintsText": {"type": "string"}, "certificateData": {"$ref": "#/definitions/RawData"}, "hasChildren": {"type": "boolean"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "issuerSerialNumber": {"type": "string"}, "keyUsage": {"type": "array", "items": {"type": "boolean"}}, "keyUsageText": {"type": "string"}, "nonce": {"type": "string"}, "parentId": {"type": "string"}, "pkirole": {"type": "string"}, "prodQualifier": {"type": "string"}, "secOCISCert": {"type": "boolean"}, "serialNo": {"type": "string"}, "services": {"type": "string"}, "signature": {"type": "string"}, "specialECU": {"type": "string"}, "state": {"type": "string", "enum": ["ISSUED", "SIGNING_REQUEST", "VIRTUAL"]}, "subject": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "subjectPublicKey": {"type": "string"}, "targetECU": {"type": "string"}, "targetSubjectKeyIdentifier": {"type": "string"}, "targetVIN": {"type": "string"}, "type": {"type": "string", "enum": ["NO_TYPE", "BACKEND_CA_CERTIFICATE", "BACKEND_CA_LINK_CERTIFICATE", "ROOT_CA_CERTIFICATE", "ROOT_CA_LINK_CERTIFICATE", "ECU_CERTIFICATE", "DIAGNOSTIC_AUTHENTICATION_CERTIFICATE", "ENHANCED_RIGHTS_CERTIFICATE", "TIME_CERTIFICATE", "VARIANT_CODE_USER_CERTIFICATE", "VARIANT_CODING_DEVICE_CERTIFICATE", "SEC_OC_IS", "VIRTUAL_FOLDER"]}, "uniqueECUID": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "string"}, "validTo": {"type": "string"}, "validityStrengthColor": {"type": "string"}, "version": {"type": "string"}}, "title": "Certificate"}, "CertificateReplacementPackageV1Input": {"type": "object", "required": ["certificate", "target"], "properties": {"certificate": {"type": "string", "description": "Certificate bytes, Base64 encoded."}, "target": {"type": "string", "description": "Replacement target. E. g.: ECU, BACKEND, ROOT", "enum": ["ECU", "BACKEND", "ROOT"]}, "targetBackendCertSubjKeyId": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}}, "title": "CertificateReplacementPackageV1Input"}, "CertificateReplacementPackageV1Result": {"type": "object", "properties": {"backendCertificate": {"type": "string"}, "ecuCertificate": {"type": "string"}, "errorMessage": {"type": "string"}, "linkCertificate": {"type": "string"}, "rootCertificate": {"type": "string"}, "target": {"type": "string", "enum": ["ECU", "BACKEND", "ROOT"]}}, "title": "CertificateReplacementPackageV1Result"}, "CertificateResult": {"type": "object", "properties": {"certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}}, "title": "CertificateResult"}, "CertificateSummary": {"type": "object", "properties": {"authorityKeyIdentifier": {"type": "string"}, "certificateType": {"type": "string"}, "errorMessage": {"type": "string"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "serialNo": {"type": "string"}, "subject": {"type": "string"}, "targetVIN": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "integer", "format": "int64"}, "validTo": {"type": "integer", "format": "int64"}}, "title": "CertificateSummary"}, "CertificateWithSNAndIssuerSNResult": {"type": "object", "properties": {"certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "issuerSerialNumber": {"type": "string", "description": "Issuer serial number encoded bytes in base64 format"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}}, "title": "CertificateWithSNAndIssuerSNResult"}, "CertificateWithSNAndUserRoleResult": {"type": "object", "properties": {"certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}, "userRole": {"type": "string", "description": "User Role Text as in specs: Supplier, Development ENHANCED, Production, After-Sales ENHANCED, After-Sales STANDARD, After-Sales BASIC, Internal Diagnostic Test ToolePTI Test Tool"}, "userRoleNumber": {"type": "string", "description": "User Role Number as in specs: 1, 2, 3, 4, 5, 67"}}, "title": "CertificateWithSNAndUserRoleResult"}, "CertificateWithSNResult": {"type": "object", "properties": {"certificateData": {"type": "string", "description": "Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "serialNumber": {"type": "string", "description": "Serial number encoded bytes in base64 format"}}, "title": "CertificateWithSNResult"}, "CheckOwnershipInput": {"type": "object", "required": ["backendCertSubjKeyId", "ecuChallenge", "serialNumber"], "properties": {"backendCertSubjKeyId": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "ecuChallenge": {"type": "string", "description": "ECU challenge, Base64 encoded"}, "serialNumber": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}}, "title": "CheckOwnershipInput"}, "DeleteCertificateModel": {"type": "object", "required": ["authorityKeyIdentifier", "serialNo"], "properties": {"authorityKeyIdentifier": {"type": "string", "description": "The authority key identifier. It is sent as Base64 encoded bytes, and the length must be 20 bytes."}, "serialNo": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}}, "title": "DeleteCertificateModel"}, "DeleteCertificatesResult": {"type": "object", "properties": {"authKeyIdentifier": {"type": "string"}, "certificate": {"type": "boolean"}, "certificateId": {"type": "string"}, "certificateType": {"type": "string"}, "message": {"type": "string"}, "publicKey": {"type": "string"}, "serialNo": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "success": {"type": "boolean"}}, "title": "DeleteCertificatesResult"}, "EcuSignRequestInput": {"type": "object", "required": ["backendSubjectKeyIdentifier", "challenge", "ecuId"], "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "challenge": {"type": "string", "description": "Challenge byte array to be signed, Base64 encoded."}, "ecuId": {"type": "string", "description": "ECU id"}, "ecuSerialNumber": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}}, "title": "EcuSignRequestInput"}, "EcuSignRequestResult": {"type": "object", "properties": {"ecuCertificate": {"type": "string"}, "errorMessage": {"type": "string"}, "expirationDate": {"type": "integer", "format": "int64"}, "serialNumber": {"type": "string"}, "signature": {"type": "string"}}, "title": "EcuSignRequestResult"}, "EncryptedPKCSImportResult": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "summary": {"type": "string"}, "importResult": {"type": "array", "items": {"$ref": "#/definitions/ImportResult"}}}, "title": "EncryptedPKCSImportResult"}, "EncryptedPKCSPackageInput": {"type": "object", "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "encryptedPKCSPackage": {"type": "string", "description": "Encrypted PKCS package generated by SigModul."}}, "title": "EncryptedPKCSPackageInput"}, "Enumeration": {"type": "object", "title": "Enumeration"}, "Extensions": {"type": "object", "properties": {"criticalExtensionOIDs": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}, "encoded": {"type": "string", "format": "byte"}, "extensionOIDs": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}, "nonCriticalExtensionOIDs": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}}, "title": "Extensions"}, "File": {"type": "object", "properties": {"absolute": {"type": "boolean"}, "absoluteFile": {"$ref": "#/definitions/File"}, "absolutePath": {"type": "string"}, "canonicalFile": {"$ref": "#/definitions/File"}, "canonicalPath": {"type": "string"}, "directory": {"type": "boolean"}, "file": {"type": "boolean"}, "freeSpace": {"type": "integer", "format": "int64"}, "hidden": {"type": "boolean"}, "name": {"type": "string"}, "parent": {"type": "string"}, "parentFile": {"$ref": "#/definitions/File"}, "path": {"type": "string"}, "totalSpace": {"type": "integer", "format": "int64"}, "usableSpace": {"type": "integer", "format": "int64"}}, "title": "File"}, "ImportResult": {"type": "object", "properties": {"authorityKeyIdentifier": {"type": "string"}, "fileName": {"type": "string"}, "message": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "success": {"type": "boolean"}}, "title": "ImportResult"}, "LocalImportInput": {"type": "object", "required": ["filePath"], "properties": {"filePath": {"type": "string", "description": "Path of the file to be imported. The file path has to provided with single slashes as separator, e.g. c:/folder/folder.ext - mind: single backslashes can not be used."}, "password": {"type": "string", "description": "The certificate password, if needed."}}, "title": "LocalImportInput"}, "Ownership": {"type": "object", "required": ["ecuProofOfOwnership"], "properties": {"ecuProofOfOwnership": {"type": "string", "description": "base64 encoded proof"}, "errorMessage": {"type": "string"}}, "title": "Ownership"}, "Principal": {"type": "object", "properties": {"name": {"type": "string"}}, "title": "Principal"}, "PublicKey": {"type": "object", "properties": {"algorithm": {"type": "string"}, "encoded": {"type": "string", "format": "byte"}, "format": {"type": "string"}}, "title": "PublicKey"}, "RDN": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "first": {"$ref": "#/definitions/AttributeTypeAndValue"}, "multiValued": {"type": "boolean"}, "typesAndValues": {"type": "array", "items": {"$ref": "#/definitions/AttributeTypeAndValue"}}}, "title": "RDN"}, "RawData": {"type": "object", "properties": {"attributesCertificateHolder": {"$ref": "#/definitions/X509AttributeCertificateHolder"}, "cert": {"$ref": "#/definitions/X509Certificate"}, "certificate": {"type": "boolean"}, "existing": {"type": "object"}, "originalBytes": {"type": "string", "format": "byte"}}, "title": "RawData"}, "SecOCISInput": {"type": "object", "required": ["backendCertSubjKeyId", "diagCertSerialNumber", "ecuCertificate", "targetECU"], "properties": {"backendCertSubjKeyId": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "diagCertSerialNumber": {"type": "string", "description": "The serial number. It is sent as Base64 encoded bytes. The maximum length is 16 bytes."}, "ecuCertificate": {"type": "string", "description": "Certificate bytes, Base64 encoded."}, "targetECU": {"type": "string", "description": "The target ECU. Maximum size of the field is 30 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}}, "title": "SecOCISInput"}, "SecureVariantCodingInput": {"type": "object", "required": ["backendSubjectKeyIdentifier", "data"], "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "The backend subject key identifier. It is sent as Base64 encoded bytes. The length is 20 bytes."}, "data": {"type": "string", "description": "Base64 representation of the data to be signed."}, "targetECU": {"type": "string", "description": "The target ECU. Maximum size of the field is 30 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}}, "title": "SecureVariantCodingInput"}, "SignatureCheckHolder": {"type": "object", "required": ["ecuCertificate", "message", "signature"], "properties": {"ecuCertificate": {"type": "string", "description": "Certificate bytes, Base64 encoded."}, "message": {"type": "string", "description": "Base64 representation of the message which needs to be checked."}, "signature": {"type": "string", "description": "Base64 representation of the signature."}}, "title": "SignatureCheckHolder"}, "UpdateCertificateMetrics": {"type": "object", "properties": {"details": {"type": "string"}, "detailsStep": {"type": "string"}, "didFailAllRetries": {"type": "boolean"}, "errorMetrics": {"type": "boolean"}, "metricsAvailable": {"type": "boolean"}, "running": {"type": "boolean"}, "status": {"type": "string"}, "updateCertificatesRetryInfo": {"$ref": "#/definitions/UpdateCertificatesRetryInfo"}}, "title": "UpdateCertificateMetrics"}, "UpdateCertificatesRetryInfo": {"type": "object", "properties": {"currentRetry": {"type": "integer", "format": "int32"}, "endpoint": {"type": "string"}, "maxRetries": {"type": "integer", "format": "int32"}, "nextRetryTime": {"type": "integer", "format": "int32"}, "nextRetryTimestamp": {"type": "integer", "format": "int64"}}, "title": "UpdateCertificatesRetryInfo"}, "UserLoginRequest": {"type": "object", "required": ["userName", "userPassword"], "properties": {"userName": {"type": "string", "description": "The user name. Length between 1 and 7 characters. Must be alpha numeric, non blank."}, "userPassword": {"type": "string", "description": "The user password as base64 encoded UTF-8 string. Length of the undecoded string must be between 9 and 100 characters. Must contain upper case, lower case, digit, and special characters."}}, "title": "UserLoginRequest"}, "VariantCoding": {"type": "object", "properties": {"errorMessage": {"type": "string"}, "expirationDate": {"type": "integer", "format": "int64"}, "serialNumber": {"type": "string", "description": "Serial number base64 string encoded"}, "signature": {"type": "string", "description": "Signature base64 string encoded"}, "varCodingCertificate": {"type": "string", "description": "Variant coding certificate base64 string encoded"}}, "title": "VariantCoding"}, "Version": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "build": {"type": "string"}, "serverVersion": {"type": "string"}, "system": {"type": "string"}}, "title": "Version"}, "Versioned": {"type": "object", "title": "Versioned"}, "X500Name": {"type": "object", "properties": {"attributeTypes": {"type": "array", "items": {"$ref": "#/definitions/ASN1ObjectIdentifier"}}, "encoded": {"type": "string", "format": "byte"}, "rdns": {"type": "array", "items": {"$ref": "#/definitions/RDN"}}}, "title": "X500Name"}, "X500Principal": {"type": "object", "properties": {"encoded": {"type": "string", "format": "byte"}, "name": {"type": "string"}}, "title": "X500Principal"}, "X509AttributeCertificateHolder": {"type": "object", "properties": {"attributes": {"type": "array", "items": {"$ref": "#/definitions/Attribute"}}, "criticalExtensionOIDs": {"type": "array", "items": {"type": "object"}}, "encoded": {"type": "string", "format": "byte"}, "extensionOIDs": {"type": "array", "items": {"type": "object"}}, "extensions": {"$ref": "#/definitions/Extensions"}, "holder": {"$ref": "#/definitions/AttributeCertificateHolder"}, "issuer": {"$ref": "#/definitions/AttributeCertificateIssuer"}, "issuerUniqueID": {"type": "array", "items": {"type": "boolean"}}, "nonCriticalExtensionOIDs": {"type": "array", "items": {"type": "object"}}, "notAfter": {"type": "string", "format": "date-time"}, "notBefore": {"type": "string", "format": "date-time"}, "serialNumber": {"type": "integer"}, "signature": {"type": "string", "format": "byte"}, "signatureAlgorithm": {"$ref": "#/definitions/AlgorithmIdentifier"}, "version": {"type": "integer", "format": "int32"}}, "title": "X509AttributeCertificateHolder"}, "X509Certificate": {"type": "object", "properties": {"basicConstraints": {"type": "integer", "format": "int32"}, "criticalExtensionOIDs": {"type": "array", "items": {"type": "string"}}, "encoded": {"type": "string", "format": "byte"}, "extendedKeyUsage": {"type": "array", "items": {"type": "string"}}, "issuerAlternativeNames": {"type": "array", "items": {"type": "array", "items": {"type": "object"}}}, "issuerDN": {"$ref": "#/definitions/Principal"}, "issuerUniqueID": {"type": "array", "items": {"type": "boolean"}}, "issuerX500Principal": {"$ref": "#/definitions/X500Principal"}, "keyUsage": {"type": "array", "items": {"type": "boolean"}}, "nonCriticalExtensionOIDs": {"type": "array", "items": {"type": "string"}}, "notAfter": {"type": "string", "format": "date-time"}, "notBefore": {"type": "string", "format": "date-time"}, "publicKey": {"$ref": "#/definitions/PublicKey"}, "serialNumber": {"type": "integer"}, "sigAlgName": {"type": "string"}, "sigAlgOID": {"type": "string"}, "sigAlgParams": {"type": "string", "format": "byte"}, "signature": {"type": "string", "format": "byte"}, "subjectAlternativeNames": {"type": "array", "items": {"type": "array", "items": {"type": "object"}}}, "subjectDN": {"$ref": "#/definitions/Principal"}, "subjectUniqueID": {"type": "array", "items": {"type": "boolean"}}, "subjectX500Principal": {"$ref": "#/definitions/X500Principal"}, "tbscertificate": {"type": "string", "format": "byte"}, "type": {"type": "string"}, "version": {"type": "integer", "format": "int32"}}, "title": "X509Certificate"}, "ZenZefiCertificate": {"type": "object", "properties": {"activeForTesting": {"type": "boolean"}, "algorithmIdentifier": {"type": "string"}, "authorityKeyIdentifier": {"type": "string"}, "baseCertificateID": {"type": "string"}, "basicConstraints": {"type": "string"}, "basicConstraintsText": {"type": "string"}, "description": {"type": "string"}, "forVsmSimulation": {"type": "boolean"}, "hasChildren": {"type": "boolean"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "issuerSerialNumber": {"type": "string"}, "keyUsage": {"type": "array", "items": {"type": "boolean"}}, "keyUsageText": {"type": "string"}, "nonce": {"type": "string"}, "parentId": {"type": "string"}, "pkirole": {"type": "string"}, "prodQualifier": {"type": "string"}, "secOCISCert": {"type": "boolean"}, "serialNo": {"type": "string"}, "services": {"type": "string"}, "signature": {"type": "string"}, "specialECU": {"type": "string"}, "state": {"type": "string", "enum": ["ISSUED", "SIGNING_REQUEST", "VIRTUAL"]}, "status": {"type": "string"}, "subject": {"type": "string"}, "subjectKeyIdentifier": {"type": "string"}, "subjectPublicKey": {"type": "string"}, "targetECU": {"type": "string"}, "targetSubjectKeyIdentifier": {"type": "string"}, "targetVIN": {"type": "string"}, "type": {"type": "string", "enum": ["NO_TYPE", "BACKEND_CA_CERTIFICATE", "BACKEND_CA_LINK_CERTIFICATE", "ROOT_CA_CERTIFICATE", "ROOT_CA_LINK_CERTIFICATE", "ECU_CERTIFICATE", "DIAGNOSTIC_AUTHENTICATION_CERTIFICATE", "ENHANCED_RIGHTS_CERTIFICATE", "TIME_CERTIFICATE", "VARIANT_CODE_USER_CERTIFICATE", "VARIANT_CODING_DEVICE_CERTIFICATE", "SEC_OC_IS", "VIRTUAL_FOLDER"]}, "uniqueECUID": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "string"}, "validTo": {"type": "string"}, "validToDateTime": {"type": "string"}, "validityStrengthColor": {"type": "string"}, "version": {"type": "string"}}, "title": "ZenZefiCertificate"}, "ZenZefiCertificateSummary": {"type": "object", "properties": {"authorityKeyIdentifier": {"type": "string"}, "certificateType": {"type": "string"}, "errorMessage": {"type": "string"}, "id": {"type": "string"}, "issuer": {"type": "string"}, "nonce": {"type": "string"}, "serialNo": {"type": "string"}, "status": {"type": "string"}, "subject": {"type": "string"}, "targetECU": {"type": "string"}, "targetVIN": {"type": "string"}, "userRole": {"type": "string"}, "validFrom": {"type": "integer", "format": "int64"}, "validTo": {"type": "integer", "format": "int64"}}, "title": "ZenZefiCertificateSummary"}, "ZenZefiCreateSignatureInput": {"type": "object", "properties": {"codingData": {"type": "string", "description": "Base64 representation of the coding data to be signed."}, "targetECU": {"type": "string", "description": "The target ECU. Maximum size of the field is 30 bytes."}, "targetVIN": {"type": "string", "description": "The target VIN. The size of the field is 17 characters."}}, "title": "ZenZefiCreateSignatureInput"}, "ZenZefiCreateSignatureResult": {"type": "object", "required": ["backendSubjectKeyIdentifier"], "properties": {"backendSubjectKeyIdentifier": {"type": "string", "description": "Backend Certificate Subject Key Identifier encoded bytes in base64 format"}, "certificateData": {"type": "string", "description": "Variant Coding User Certificate encoded bytes in base64 format"}, "errorMessage": {"type": "string"}, "serialNumber": {"type": "string", "description": "Variant Coding User Certificate Serial Number encoded bytes in base64 format"}, "signature": {"type": "string", "description": "Signed data encoded bytes in base64 format"}, "validTo": {"type": "integer", "format": "int64", "description": "Variant Coding User Certificate expiration timestamp"}}, "title": "ZenZefiCreateSignatureResult"}}}