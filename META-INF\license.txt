 Copyright 2012-2017 <PERSON><PERSON> (http://www.gunnarmorling.de/)
 and/or other contributors as indicated by the @authors tag. See the
 copyright.txt file in the distribution for a full listing of all
 contributors.

 MapStruct is licensed under the Apache License, Version 2.0 (the
 "License"); you may not use this software except in compliance with the
 License. You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

------------------------------------------------------------------------

 MAPSTRUCT SUBCOMPONENTS WITH DIFFERENT COPYRIGHT OWNERS

 The MapStruct distribution (ZIP, TAR.GZ) as well as the MapStruct
 library (JAR) include FreeMarker, a software developed by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>. FreeMarker is licensed
 under the same license as MapStruct itself - Apache License, Version
 2.0 - but the copyright owners are the aforementioned individuals.

 The MapStruct distribution (ZIP, TAR.GZ) as well as the MapStruct
 library (JAR) include a number of files that are licensed by the
 Apache Software Foundation under the same license as MapStruct itself -
 Apache License, Version 2.0 - but the copyright owner is the Apache
 Software Foundation. These files are:

     freemarker/ext/jsp/web-app_2_2.dtd
     freemarker/ext/jsp/web-app_2_3.dtd
     freemarker/ext/jsp/web-app_2_4.xsd
     freemarker/ext/jsp/web-app_2_5.xsd
     freemarker/ext/jsp/web-jsptaglibrary_1_1.dtd
     freemarker/ext/jsp/web-jsptaglibrary_1_2.dtd
     freemarker/ext/jsp/web-jsptaglibrary_2_0.xsd
     freemarker/ext/jsp/web-jsptaglibrary_2_1.xsd
