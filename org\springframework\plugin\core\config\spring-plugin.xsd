<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns="http://www.springframework.org/schema/plugin" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:beans="http://www.springframework.org/schema/beans"
    xmlns:tool="http://www.springframework.org/schema/tool"
    targetNamespace="http://www.springframework.org/schema/plugin" elementFormDefault="qualified"
    attributeFormDefault="unqualified">

    <xsd:import namespace="http://www.springframework.org/schema/tool" />

    <xsd:element name="list" type="pluginType">
        <xsd:annotation>
            <xsd:documentation>
                Collects all beans implementing the
                configured interface and exposes them as list under the
                configured id. Can be used in a nested manner, too. Will
                regard the order of the collected beans if they either
                implement Ordered or are annotated with @Order.
            </xsd:documentation>
            <xsd:appinfo>
                <tool:annotation>
                    <tool:exports identifier="@id"
                        type="java.util.List" />
                </tool:annotation>
            </xsd:appinfo>
        </xsd:annotation>
    </xsd:element>

    <xsd:element name="registry" type="pluginType">
        <xsd:annotation>
            <xsd:documentation>
                Creates a PluginRegistry for all beans
                implementing the configured type. Thus the type
                configured in the class attribute has to extend Plugin.
                The registry will consider the order of the plugins if
                they implement Ordered or are annotated with @Order.
            </xsd:documentation>
            <xsd:appinfo>
                <tool:annotation>
                    <tool:exports type="org.springframework.plugin.core.OrderAwarePluginRegistry" />
                </tool:annotation>
            </xsd:appinfo>
        </xsd:annotation>
    </xsd:element>

    <xsd:complexType name="pluginType">
        <xsd:attribute name="id" type="xsd:string" />
        <xsd:attribute name="class" type="classType" />
        <xsd:attribute name="init-factories" type="xsd:boolean" default="false" />
    </xsd:complexType>

    <xsd:simpleType name="classType">
        <xsd:annotation>
            <xsd:appinfo>
                <tool:annotation kind="direct">
                    <tool:expected-type type="java.lang.Class" />
                </tool:annotation>
            </xsd:appinfo>
        </xsd:annotation>
        <xsd:union memberTypes="xsd:string" />
    </xsd:simpleType>

</xsd:schema>