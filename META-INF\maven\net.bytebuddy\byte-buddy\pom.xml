<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-parent</artifactId>
        <version>1.10.8</version>
    </parent>

    <artifactId>byte-buddy</artifactId>
    <packaging>jar</packaging>

    <name>Byte Buddy (without dependencies)</name>
    <description>
        Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.
    </description>

    <!--
      Because of the Shade plugin, the variables of this POM are resolved with the current work station's values the
      dependency reduced POM. The POM that is presented to the Shade plugin is already resolved which is why it is
      not possible to prevent this behavior as the profiles are not removed form the POM. This does not effect the
      usability of the POM for a dependency but might confuse users that read the POM. The Shade plugin also removes
      this comment from deployment such that there is no easy way to explain this in the deployed POM.
    -->

    <properties>
        <shade.source>org.objectweb.asm</shade.source>
        <shade.target>net.bytebuddy.jar.asm</shade.target>
        <packages.list>
            net.bytebuddy,
            net.bytebuddy.agent.builder,
            net.bytebuddy.asm,
            net.bytebuddy.build,
            net.bytebuddy.description,
            net.bytebuddy.description.annotation,
            net.bytebuddy.description.enumeration,
            net.bytebuddy.description.field,
            net.bytebuddy.description.method,
            net.bytebuddy.description.modifier,
            net.bytebuddy.description.type,
            net.bytebuddy.dynamic,
            net.bytebuddy.dynamic.loading,
            net.bytebuddy.dynamic.scaffold,
            net.bytebuddy.dynamic.scaffold.inline,
            net.bytebuddy.dynamic.scaffold.subclass,
            net.bytebuddy.implementation,
            net.bytebuddy.implementation.attribute,
            net.bytebuddy.implementation.auxiliary,
            net.bytebuddy.implementation.bind,
            net.bytebuddy.implementation.bind.annotation,
            net.bytebuddy.implementation.bytecode,
            net.bytebuddy.implementation.bytecode.assign,
            net.bytebuddy.implementation.bytecode.assign.primitive,
            net.bytebuddy.implementation.bytecode.assign.reference,
            net.bytebuddy.implementation.bytecode.collection,
            net.bytebuddy.implementation.bytecode.constant,
            net.bytebuddy.implementation.bytecode.member,
            net.bytebuddy.matcher,
            net.bytebuddy.pool,
            net.bytebuddy.utility,
            net.bytebuddy.utility.privilege,
            net.bytebuddy.utility.visitor,
            ${shade.target},
            ${shade.target}.signature,
            ${shade.target}.commons
        </packages.list>
    </properties>

    <!--
      IDEs might complain about the following dependency not being OSGI compatible. While this is true, it does not
      matter as the dependency is repackaged by the Shade plugin and becomes part of this module's jar artifact.
    -->

    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>byte-buddy-dep</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- Shade the ASM dependency. -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${version.plugin.shade}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <createSourcesJar>${bytebuddy.extras}</createSourcesJar>
                            <shadeSourcesContent>true</shadeSourcesContent>
                            <relocations>
                                <relocation>
                                    <pattern>${shade.source}</pattern>
                                    <shadedPattern>${shade.target}</shadedPattern>
                                </relocation>
                            </relocations>
                            <filters>
                                <filter>
                                    <artifact>org.ow2.asm:*</artifact>
                                    <excludes>
                                        <exclude>**/module-info.class</exclude>
                                        <exclude>**/LICENSE</exclude>
                                        <exclude>**/NOTICE</exclude>
                                    </excludes>
                                </filter>
                                <filter>
                                    <artifact>org.ow2.asm:asm-commons</artifact>
                                    <includes>
                                        <include>org/objectweb/asm/commons/AnnotationRemapper.class</include>
                                        <include>org/objectweb/asm/commons/ClassRemapper.class</include>
                                        <include>org/objectweb/asm/commons/FieldRemapper.class</include>
                                        <include>org/objectweb/asm/commons/MethodRemapper.class</include>
                                        <include>org/objectweb/asm/commons/ModuleHashesAttribute.class</include>
                                        <include>org/objectweb/asm/commons/ModuleRemapper.class</include>
                                        <include>org/objectweb/asm/commons/RecordComponentRemapper.class</include>
                                        <include>org/objectweb/asm/commons/Remapper.class</include>
                                        <include>org/objectweb/asm/commons/SignatureRemapper.class</include>
                                        <include>org/objectweb/asm/commons/SimpleRemapper.class</include>
                                    </includes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>net.bytebuddy.build.Plugin$Engine$Default</mainClass>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>META-INF/LICENSE</resource>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                        <version>${version.asm}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm-commons</artifactId>
                        <version>${version.asm}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <!-- Disable pitest as it fails for empty modules -->
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${version.plugin.pitest}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- Skip spotbugs as it fails for empty modules. -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${version.plugin.spotbugs}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- Create manifest file which is required for creating an OSGi bundle. -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${version.plugin.jar}</version>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <!--&lt;!&ndash; Configure OSGi bundle. Due to interaction with the shade plugin, IDEs might identify the following configuration as illegal. &ndash;&gt;-->
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>${version.plugin.bundle}</version>
                <executions>
                    <execution>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <instructions>
                        <Multi-Release>true</Multi-Release>
                        <!-- Cannot use wildcards because of use of the shade plugin which imports from 'byte-buddy-dep'. -->
                        <Export-Package>${packages.list}</Export-Package>
                    </instructions>
                </configuration>
            </plugin>
            <!-- Inject a module-info.class file into the shaded jar. -->
            <plugin>
                <groupId>codes.rafael.modulemaker</groupId>
                <artifactId>modulemaker-maven-plugin</artifactId>
                <version>${version.plugin.modulemaker}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                        <version>${version.asm}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>inject-module</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <name>net.bytebuddy</name>
                    <multirelease>true</multirelease>
                    <packages>${packages.list}</packages>
                    <exports>${packages.list}</exports>
                    <static-requires>
                        java.instrument,
                        jdk.unsupported,
                        net.bytebuddy.agent
                    </static-requires>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>extras</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- The source jar is generated by the shade plugin and we do not want to override the shaded source jar. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${version.plugin.source}</version>
                        <configuration>
                            <skipSource>true</skipSource>
                        </configuration>
                    </plugin>
                    <!-- The shadowed source files of this module need to be included explicitly to create a javadoc artifact.-->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${version.plugin.javadoc}</version>
                        <configuration>
                            <includeDependencySources>true</includeDependencySources>
                            <dependencySourceIncludes>
                                <dependencySourceInclude>${project.groupId}:byte-buddy-dep</dependencySourceInclude>
                            </dependencySourceIncludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
